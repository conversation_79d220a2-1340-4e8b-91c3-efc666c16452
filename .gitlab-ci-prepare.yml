.cleanup_template: &cleanup
  stage: prepare
  interruptible: true
  script:
    - npm i
    - node scripts/stack-cleanup.js -p $CLEANUP_PROXY

check-variables:
  stage: prepare
  variables:
    MESSAGE_REPLICAS_STAGING: "O ambinete staging suporta no máximo 4 replicas cypress simultâneas"
    TEST_ENVIRONMENT: "none"
    CLEANUP: "true"
  script:
    - printenv
    - if [ "$CYPRESS_REPLICAS" -gt 2 && $TEST_ENVIRONMENT == 'staging' ]; then echo $MESSAGE_REPLICAS_STAGING; exit 1; fi
  rules:
    - if: $TEST_ENVIRONMENT == 'staging' || $TEST_ENVIRONMENT == 'prod'


check-oci-variables:
  stage: prepare
  variables:
    MESSAGE_REPLICAS_STAGING: "O ambinete staging suporta no máximo 4 replicas cypress simultâneas"
    TEST_ENVIRONMENT: "none"
    CLEANUP: "true"
  script:
    - printenv
    - if [ "$CYPRESS_REPLICAS" -gt 2 && $TEST_ENVIRONMENT == 'oci-staging' ]; then echo $MESSAGE_REPLICAS_STAGING; exit 1; fi
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod'
  tags:
    - deploy-teste-auto-oci-shell

check-oci-t3-variables:
  stage: prepare
  variables:
    MESSAGE_REPLICAS_STAGING: "O ambinete staging suporta no máximo 4 replicas cypress simultâneas"
    TEST_ENVIRONMENT: "none"
    CLEANUP: "true"
  script:
    - printenv
    - if [ "$CYPRESS_REPLICAS" -gt 2 && $TEST_ENVIRONMENT == 'oci-staging-t3' ]; then echo $MESSAGE_REPLICAS_STAGING; exit 1; fi
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod-t3'
  tags:
    - deploy-teste-auto-oci-t3-shell

check-oci-t5-variables:
  stage: prepare
  variables:
    MESSAGE_REPLICAS_STAGING: "O ambinete staging suporta no máximo 4 replicas cypress simultâneas"
    TEST_ENVIRONMENT: "none"
    CLEANUP: "true"
  script:
    - printenv
    - if [ "$CYPRESS_REPLICAS" -gt 2 && $TEST_ENVIRONMENT == 'oci-staging-t5' ]; then echo $MESSAGE_REPLICAS_STAGING; exit 1; fi
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod-t5'
  tags:
    - deploy-teste-auto-oci-t5-shell

check-ufg-t4-variables:
  stage: prepare
  variables:
    MESSAGE_REPLICAS_STAGING: "O ambinete staging suporta no máximo 4 replicas cypress simultâneas"
    TEST_ENVIRONMENT: "none"
    CLEANUP: "true"
  script:
    - printenv
    - if [ "$CYPRESS_REPLICAS" -gt 2 && $TEST_ENVIRONMENT == 'ufg-staging-t4' ]; then echo $MESSAGE_REPLICAS_STAGING; exit 1; fi
  rules:
    - if: $TEST_ENVIRONMENT == 'ufg-prod-t4'
  tags:
    - deploy-teste-auto-ufg-t4-shell


cleanup-staging:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "staging" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto

cleanup-oci-staging:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "oci-staging" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-oci-shell

cleanup-oci-staging-t3:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "oci-staging-t3" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-oci-t3-shell

cleanup-oci-staging-t5:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "oci-staging-t5" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-oci-t5-shell

cleanup-ufg-staging-t4:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "ufg-staging-t4" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-ufg-t4-shell

cleanup-prod:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "prod" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - swarm
    - locaweb
    - manager

cleanup-oci-prod:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "oci-prod" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-oci-shell

cleanup-oci-prod-t3:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "oci-prod-t3" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-oci-t3-shell

cleanup-oci-prod-t5:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "oci-prod-t5" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-oci-t5-shell

cleanup-ufg-prod-t4:
  <<: *cleanup
  rules:
    - if: '$TEST_ENVIRONMENT == "ufg-prod-t4" && $CLEANUP == "true"'
      when: always
    - when: never
  tags:
    - deploy-teste-auto-ufg-t4-shell

.build-cypress-template:
  stage: prepare
  script:
    - npm i
    - node scripts/build-cypress.js -b $CI_COMMIT_REF_SLUG
  artifacts:
    paths:
      - zw/cypress.build.yml

build-cypress-prod:
  extends: .build-cypress-template
  rules:
    - if: $TEST_ENVIRONMENT == 'prod' || $TEST_ENVIRONMENT == 'staging'
      when: always
  tags:
    - deploy-teste-auto

build-cypress-oci-prod:
  extends: .build-cypress-template
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod'
      when: always
  tags:
    - deploy-teste-auto-oci-shell

build-cypress-oci-prod-t3:
  extends: .build-cypress-template
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod-t3'
      when: always
  tags:
    - deploy-teste-auto-oci-t3-shell

build-cypress-oci-prod-t5:
  extends: .build-cypress-template
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod-t5'
      when: always
  tags:
    - deploy-teste-auto-oci-t5-shell

build-cypress-ufg-prod-t4:
  extends: .build-cypress-template
  rules:
    - if: $TEST_ENVIRONMENT == 'ufg-prod-t4'
      when: always
  tags:
    - deploy-teste-auto-ufg-t4-shell

.deploy-app_template: &deploy-app
  stage: prepare
  script:
    - npm i
    - node scripts/deploy-app-ci.js -s $CI_PIPELINE_ID -d $DOMAIN -i $IP -b $CI_COMMIT_REF_SLUG -ab $APP_BRANCHS
  artifacts:
    paths:
      - docker/swarm/.env
      - docker/swarm/cypress.yml
      - zw/.env
      - zw/cypress.yml

deploy-app-staging:
  <<: *deploy-app
  tags:
    - deploy-teste-auto
  rules:
    - if: $TEST_ENVIRONMENT == 'staging'
      variables:
        IP: "************"
        DOMAIN: "test.pactosolucoes.com.br"
    - if: $TEST_ENVIRONMENT == 'staging' && $CLEANUP == 'true'
      needs: [cleanup-staging]

deploy-app-prod:
  <<: *deploy-app
  tags:
    - swarm
    - locaweb
    - manager
  needs:
    - job: cleanup-prod
      optional: true
  rules:
    - if: $TEST_ENVIRONMENT == 'prod'
      variables:
        IP: "**********"
        DOMAIN: "pactoteste.com"

deploy-app-oci-prod:
  <<: *deploy-app
  tags:
    - deploy-teste-auto-oci-shell
  needs:
    - job: cleanup-oci-prod
      optional: true
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod'
      variables:
        IP: "***********"
        DOMAIN: "t2.pactoteste2.com"

deploy-app-oci-prod-t3:
  <<: *deploy-app
  tags:
    - deploy-teste-auto-oci-t3-shell
  needs:
    - job: cleanup-oci-prod-t3
      optional: true
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod-t3'
      variables:
        IP: "***********"
        DOMAIN: "t3.pactoteste2.com"

deploy-app-oci-prod-t5:
  <<: *deploy-app
  tags:
    - deploy-teste-auto-oci-t5-shell
  needs:
    - job: cleanup-oci-prod-t5
      optional: true
  rules:
    - if: $TEST_ENVIRONMENT == 'oci-prod-t5'
      variables:
        IP: "*********"
        DOMAIN: "t5.pactoteste2.com"

deploy-app-ufg-prod-t4:
  <<: *deploy-app
  tags:
    - deploy-teste-auto-ufg-t4-shell
  needs:
    - job: cleanup-ufg-prod-t4
      optional: true
  rules:
    - if: $TEST_ENVIRONMENT == 'ufg-prod-t4'
      variables:
        IP: "*************"
        DOMAIN: "t4.pactoteste2.com"
