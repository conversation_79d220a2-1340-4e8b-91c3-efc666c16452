# Teste auto

Este projeto é uma aplicação de teste automatizado. Ele foi criado para facilitar a execução de testes em diferentes partes do nosso sistema.

## Instalação

Para instalar as dependências do projeto, execute o seguintes comandos:

Na pasta raiz do projeto, execute:

```bash
npm install
```

Na pasta zw, execute:

```bash
npm install
```

## Uso


Como conectar no ambiente de teste criado na pipeline de testes:

Este comando irá abrir uma aplicação visual para você escolher o ambiente de teste que deseja conectar.

```
npm run painel
```

Você pode conectar no ambiente de teste criado na pipeline, também por linha de comando:

```
npm run pipec
```

(Conectar no ambiente de desenvolvimento local. Neste ambiente você precisa que o sistema esteja rodando localmente com chuck-norris. Veja a doc aqui: https://gitlab.com/Plataformazw/chuck-norris):

```
npm run open:local
```


Para rodar uma nova pipeline de teste

```
npm run piper
```

## Pipeline CI/CD

Este repositório utiliza uma pipeline de Integração Contínua/Entrega Contínua (CI/CD) para testar e validar as liberações de versões do sistema Pacto.

A pipeline é agendada para as terças e quintas-feiras. Além disso, ela pode ser acionada manualmente quando necessário.

1. **Preparação do ambiente**: O ambiente no cluster de teste automatizado é preparado para a execução dos testes.

2. **Instalação de dependências**: As dependências do projeto são instaladas para garantir que tudo está atualizado e pronto para os testes.

3. **Execução de testes**: Os testes automatizados são executados para garantir que todas as funcionalidades do sistema Pacto estão funcionando como esperado.

4. **Validação de liberação**: Se os testes passarem, a pipeline valida a liberação da nova versão do sistema Pacto. Isso garante que apenas versões estáveis e testadas do sistema sejam liberadas.

Essa pipeline ajuda a manter a qualidade do sistema Pacto, garantindo que todas as novas alterações e versões sejam testadas e validadas antes de serem liberadas.

# Versões:

| **Release**   | **Docker Tags**                                                                                                                                            | **Branchs**                                                                                                                                                  |
|---------------|------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Beta (5%)** | zw=release-rc, adm-front=release-rc, treino-front=release-rc, treino=release-rc                                                        | zw=release/RC, zw-ui=release/RC, treino=release/RC                                                                            |
| **Pratique**  | zw=release-rc-pratique,treino=release-rc-pratique,treino-front=release-rc-pratique,adm-core-ms=release-rc-pratique,vendas-online-front=release-rc-pratique | zw=release/RC-PRATIQUE, treino=release/RC-PRATIQUE, treino-ui=release/RC-PRATIQUE, adm-core-ms=release/RC-PRATIQUE, vendas-online-front=release/RC-PRATIQUE  |

## Infras e versões:

Última stack de resultados para liberação de versão: 01/10/2024 10:40

| Teste Aprovado | Zona                  | Versão              |
|----------------|-----------------------|---------------------|
| ✅             | ZW5 e ZW801           | RELEASE/RC          |
| ✅             | ZW73 e ZW92           | RELEASE/RC-PRATIQUE |
| ✅             | Todas as outras zonas | latest              |

Aqui está o README atualizado com a descrição da arquitetura **NeoGeo**, incluindo o exemplo de **service**, organização de pastas, e a imagem do menu "Explorar":

---

# 📘 Arquitetura de Teste Automatizado - NeoGeo

A arquitetura **NeoGeo** organiza os testes de forma modular e escalável, utilizando **Cypress** como framework principal. Cada camada da arquitetura tem uma responsabilidade específica, e a organização dos testes segue o padrão dos **recursos do sistema**, encontrados no menu "Explorar".

---

## 📂 Estrutura de Pastas  

Os testes são organizados de acordo com a hierarquia do menu "Explorar" no sistema, que segue os níveis: **Módulo**, **Grupo**, **Subgrupo** e **Recurso**.

**Exemplo de estrutura para o recurso "Negociação":**

```
zw/cypress/e2e/Kula/ADM/Operacoes/OperacoesDeVenda/Negociacao/
```

- **ADM** → Módulo  
- **Operacoes** → Grupo  
- **OperacoesDeVenda** → Subgrupo  
- **Negociacao** → Recurso  

Dentro da pasta do recurso, ficam todos os arquivos de teste relacionados.

---

### Menu "Explorar" no Sistema  
![Menu Explorar](explorar.png)  

Na imagem acima:  
- **Módulo**: Administrativo  
- **Grupo**: Operações  
- **Subgrupo**: Operações de Venda  
- **Recurso**: Negociação  

---

## 🔍 Descrição das Camadas  

### **1️⃣ Camada de Testes (`cypress/e2e/Kula`)**  
Contém os **arquivos de teste principais** que seguem a hierarquia do menu "Explorar". Esses arquivos utilizam as demais camadas da arquitetura para interagir com a interface e as APIs do sistema.

### **2️⃣ Camada de Factories (`cypress/support/Kula/factories/`)**  
Contém métodos simples para **gerar dados** que serão utilizados nos testes.

> **Exemplo de Factory:**
```typescript
import { Cliente } from "@models/Cliente";
import * as faker from "faker-br";

export default class ClienteFactory {
    static criarCliente(empresa: number): Cliente {
        return {
            nome: faker.name.findName(),
            cpf: faker.br.cpf(),
            celular: faker.phone.phoneNumber("###########"),
            email: faker.internet.email(),
            cep: "74681202",
            sexo: "M",
            empresa,
            rg: "",
            bairro: "",
            dataNascimento: "2000-03-12T13:34:34.799Z"
        };
    }
}
```

---

### **3️⃣ Camada de Models (`cypress/support/Kula/models/`)**  
Define as interfaces TypeScript para garantir a padronização dos objetos utilizados nos testes.

> **Exemplo de Model:**
```typescript
export interface Cliente {
    nome: string;
    cpf: string;
    celular: string;
    email: string;
    cep: string;
    sexo: string;
    empresa: number;
    rg: string;
    bairro: string;
    dataNascimento: string;
}
```

---

### **4️⃣ Camada de Pages (`cypress/support/Kula/pages/<Diretorios do recurso>`)**  
📂 **Organização das Pastas nesta Camada**  

As pastas desta camada estão organizadas de acordo com as camadas de teste definidas em **Estrutura de Pastas**, seguindo a mesma hierarquia do menu **"Explorar"** 🗂️.  

📖 Nesta documentação, utilizamos o nome 🏷️ para referenciar essa estrutura de pastas.  

📌 Todas as classes desta camada estão localizadas dentro do **📁 [nome do diretório]** e contêm métodos que:  
🔹 Interagem com o **DOM** 🖥️  
🔹 Executam ações na **interface do usuário** 🖱️💡

Por exemplo na pasta `cypress/support/Kula/pages/ADM/Operacoes/OperacoesDeVenda/Negociacao` tem um exemplo:

> **Exemplo de Page:**
```typescript
import NegociacaoLocators from './NegociacaoLocator';
import Usuario from '@models/Usuario';
import { Plano } from '@models/Plano';
import { ClientePessoa } from '@models/Cliente';
import NotificacaoPage from '@pages/Layout/Notificacao/NotificacaoPage';
import ExplorarNavigation from '@pages/Layout/Menu/ZWUI/ExplorarNavigation';

export default class NegociacaoPage {
  static negociarPlanoNormal(usuario: Usuario, dadosPlano: Plano, dadosCliente: ClientePessoa): void {
    ExplorarNavigation.abrirNegociacao();

    cy.get(NegociacaoLocators.buscarCliente).type(dadosCliente.pessoa.nome);
    cy.get(NegociacaoLocators.listaCliente).contains(dadosCliente.pessoa.nome.toLowerCase()).click();
    cy.get(NegociacaoLocators.selectPlano).select(dadosPlano.descricao);
    cy.get(NegociacaoLocators.modalSelectCondicaoPagamento).click();
    cy.get(NegociacaoLocators.selectCondicaoPagamento).select('A VISTA');
    cy.intercept('POST', '**/negociacao/gravar').as('gravarNegociacao');
    cy.get(NegociacaoLocators.btnEvniarLinkPagamento).click();
    cy.get(NegociacaoLocators.inputSenha).type(usuario.senha);
    cy.get(NegociacaoLocators.btnConfirmarSenha).click();
    NotificacaoPage.confirmarNotificacao('Contrato lançado com sucesso');
  }
}
```


#### **4.1 Camada de Locators (`cypress/support/Kula/pages/<Diretorios do recurso>/locators/`)**  
Define todos seletores CSS dos elementos utilizados nas classes Page, para facilitar a reutilização.

> **Exemplo de Locators:**
```typescript
const negociacaoLocators = {
    buscarCliente: '[title="Busca rápida"]',
    inputPlano: '#input-plano',
    listaCliente: '.lista-cliente',
    selectPlano: '#input-plano',
    btnReceber: '#btn-receber',
    btnEvniarLinkPagamento: '#btn-enviar',
    inputPassword: '#aut-input-psw',
    btnConfirm: '.ng-star-inserted',   
    selectCondicaoPagamento: '#input-condicao',
    modalSelectCondicaoPagamento: '[label="Condição de pagamento"]',
    inputSenha: '#aut-input-psw',
    btnConfirmarSenha: '#aut-btn-confirm',
    campoDataInicialContrato: '.zebra-row > :nth-child(3)',
    campoDataFinalContrato: '.zebra-row > :nth-child(4)',
    fecharAba: '.close-wrapper',
};

export default negociacaoLocators;
```

---

### **5️⃣ Camada de Services (`cypress/support/Kula/services/<Diretorio com nome do projeto de api>`)**  
`<Diretorio com nome do projeto de api>` é o nome projeto gitlab que implementa esta api, por exemplo adm-ms, autenticacao-ms, discovery-ms.
Faz chamadas HTTP para criar registros e configurar as pré-condições dos testes.

> **Exemplo de Service:**
```typescript
import { Cliente, ClientePessoa } from "@models/Cliente";
import ClienteFactory from "@factories/ClienteFactory";
import EnvUtils from "@utils/EnvUtils";
import { Empresa } from "@models/EmpresasEnum";

export default class AdmMsClientesApi {
  static incluirCliente(empresa: Empresa): Cypress.Chainable<ClientePessoa> {
    return cy.getCookies().then((cookies) => {
      cy.log(`Incluindo cliente para a empresa ${empresa.nome}`);
      const token = cookies.find((cookie) => cookie.name === "token").value;
      const admMsUrl = EnvUtils.admMsUrl();

      const cliente: Cliente = ClienteFactory.criarCliente(empresa.codigo);

      return cy
        .request({
          method: "POST",
          url: `${admMsUrl}/v1/cliente`,
          headers: {
            authorization: `Bearer ${token}`,
          },
          body: cliente,
        })
        .then((response) => {
          expect(response.status).to.eq(200);
          const cliente = response.body.content as ClientePessoa;
          cy.log(`Cliente ${cliente.pessoa.nome} incluído com sucesso`);
          return cy.wrap(cliente);
        });
    });
  }
}
```

### **6️⃣ Camada de Utils (`cypress/support/Kula/utils`)**  
Contém funções para manipulação de textos, datas, variáveis de ambiente. 

> **Exemplo de Util:**
```typescript
export default new class DateUtils {
  parseDateBR(): string {
    const dataAtual = new Date();
    return new Date(dataAtual.getTime()).toISOString();
  }
}
```
---

## 🚀 Benefícios da Arquitetura **NeoGeo**  
✔ **Organização Modular**: A estrutura é clara e escalável.  
✔ **Reutilização de Código**: Facilita a manutenção e evita duplicação de lógica.  
✔ **Facilidade na Navegação**: O padrão espelha a hierarquia do sistema, facilitando a localização de testes.  
✔ **Manutenção Simplificada**: Alterações afetam apenas componentes específicos, reduzindo o impacto geral.

Essa arquitetura permite que os testes sejam confiáveis, eficientes e fáceis de gerenciar, garantindo qualidade contínua no sistema. 🚀