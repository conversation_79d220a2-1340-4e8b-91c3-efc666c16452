const { defineConfig } = require("cypress");
const { cloudPlugin } = require("cypress-cloud/plugin");
const { getFixtureByEnvironment } = require("./cypress/support/environments");
const axios = require('axios');

module.exports = defineConfig({
  chromeWebSecurity: false,
  video: false,
  videoUploadOnPasses: false,
  projectId: 'wz4u88',
  viewportWidth: 1920,
  viewportHeight: 1280,
  e2e: {
    experimentalSessionAndOrigin: true,
    numTestsKeptInMemory: 3,
    retries: {
      runMode: 3,
      openMode: 0
    },
    env: {
      TEST_ENVIRONMENT: 'PIPE',
      DISCOVERY_URL: 'http://2045621633-discovery.pactoteste.com',
    },
    async setupNodeEvents(on, config) {

      on('task', {
        log(message) {
          console.log(message);
          return null;
        }
      });

      config = await setEnvsByEnvironment(config);

      require('@cypress/grep/src/plugin')(config);

      if (config.env.CYPRESS_SORRY && (config.env.CYPRESS_SORRY === true || config.env.CYPRESS_SORRY.toUpperCase() === 'TRUE')) {
        console.info('CYPRESS_SORRY é true, vou utilizar o cypress sorry como orquestrador');
        configResult = await cloudPlugin(on, config);
        return configResult;
      } else {
        console.info('CYPRESS_SORRY é false, vou utilizar o cypress cloud como orquestrador');
        return config;
      }
    },
    baseUrl: 'http://2045621633-zw.pactoteste.com/ZillyonWeb',
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}',
  },
})

async function setEnvsByEnvironment(config) {
  if (config.env.TEST_ENVIRONMENT === undefined || config.env.TEST_ENVIRONMENT === null) {
    console.info('Ambiente não informado, utilizando ambiente ZW74');
    config.env.TEST_ENVIRONMENT = 'ZW74';
  }

  const fixture = getFixtureByEnvironment(config.env.TEST_ENVIRONMENT);
  config.env.usuarios = fixture.usuarios;
  if (!fixture.usuarios) {
    console.error('Não foi possível encontrar o ambiente informado');
    process.exit(1);
  }

  config.env.USUARIO_PACTOBR = fixture.usuarios.PACTOBR;
  config.env.USUARIO_PACTOBR.chave = fixture.chave;

  Object.keys(fixture).forEach(key => {
    config.env[key] = fixture[key];
  });

  if (fixture.baseUrl.includes('http://') || fixture.baseUrl.includes('https://')) {
    config.baseUrl = fixture.baseUrl;
  }

  if (fixture.discoveryMsUrl.includes('http://') || fixture.discoveryMsUrl.includes('https://')) {
    config.env.DISCOVERY_URL = fixture.discoveryMsUrl;
  }

  config = await setDiscoveryUrls(config, fixture.chave);

  console.info(`Ambiente configurado: ${config.env.TEST_ENVIRONMENT}`);
  console.info(`Base url do ambiente é: ${config.baseUrl}`);
  console.info(`DiscoveryMs do ambiente é: ${config.env.DISCOVERY_URL}`);

  return config;
}

async function setDiscoveryUrls(config, chave) {
  try {
    const response = await axios.get(`${config.env.DISCOVERY_URL}/find/${chave}`);
    if (response.status !== 200) {
      console.error(`Não foi possível encontrar as urls do ambiente a partir do discoveryMs ${config.env.DISCOVERY_URL}/find/${chave}`);
      console.error(`Status code: ${response.status}`);
      process.exit(1);
    }
    const serviceUrls = response.data.content.serviceUrls;
    config.env.SERVICE_URLS = serviceUrls;
    return config;
  } catch (error) {
    console.error(`Não foi possível encontrar as urls do ambiente a partir do discoveryMs ${config.env.DISCOVERY_URL}/find/${chave}`);
    process.exit(1);
  }
}