//Teste registra um acesso manual e valida no BI ADM Gestão de acessos

import biGestaoAcessos from '../../../support/pages/BIGestaoAcessos';
import RelatorioPage from "../../../support/pages/angular/adm/RelatorioPage";

describe('BI Gestão de Acessos', () => {


    it('Cadastrar acesso de aluno na tela "Gestão de Acessos', () => {
        const matricula = '000114';
       RelatorioPage.CadastrarAlunoGestaoDeAcesso(matricula)
    });
    it('Validar no BI Gestão de Acessos', () => {
      const aluno = 'ENILDAVERISSIMO'
        RelatorioPage.validarBIGestaoAcessos(aluno)
     });
    });








