import RelatorioPage from "../../../support/pages/angular/adm/RelatorioPage";

describe('Relatorio geral de clientes', function () {

    beforeEach(() => {

    })

    it('Filtros de dados pessoais ', function () {
    RelatorioPage.filtroDadosPessoais()
    })
    it('Filtros de dados pessoais- Aniversáriantes do Mês ', function () {
    RelatorioPage.filtroAniversarianteMes()
    })
    it('Filtros de dados de contrato - Data de Vencimento', function () {
    RelatorioPage.filtroDataVencimento()
    })
    it('Filtros de dados de contrato - Evento', function () {
    RelatorioPage.filtroEvento()
    })
    it('Filtros de dados de contrato - Plano', function () {
    RelatorioPage.filtroPlano()
    })
    it('Filtros de dados de contrato - Situação', function () {
    RelatorioPage.filtroSituacao()
    })
    it('Filtros de dados de contrato- Modalidade', function () {
    RelatorioPage.filtroModalidade()
    })
    it('Filtros de dados de contrato- Empresas', function () {
    RelatorioPage.filtroEmpresa()
    })
})
