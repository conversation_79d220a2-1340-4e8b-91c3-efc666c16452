
import vendaPlano from '../../../support/pages/ConversaoDeVenda'
import RelatorioPage from "../../../support/pages/angular/adm/RelatorioPage";

const nomes = require('../../../fixtures/nomes.json');

const nomeAluno = nomes[Math.floor(Math.random() * 950 + 1)].replace(/[^a-z0-9]/gi, '');

describe('Validar BI de conversão de venda em um dia especifico', () => {

    it('BI de conversão de venda', () => {
    RelatorioPage.ConverterVendas(nomeAluno)
    });
});
