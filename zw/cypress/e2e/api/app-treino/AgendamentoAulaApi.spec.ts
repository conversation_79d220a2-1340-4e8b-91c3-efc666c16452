import { FANTASIA } from "@models/EmpresasEnum";
import AdmMsClientesApi from "@services/AdmMs/AdmMsClientesApi";
import { AppTreinoApi } from "@services/api/AppTreinoApi";
import EnvUtils from "@utils/EnvUtils";
import DateUtils from "@utils/DateUtils";
import ApiAutenticacao from "@services/Autenticacao/AutenticacaoLoginApi";
import { ZwBootApi } from "@services/ZwBoot/ZwBootNegociacaoApi";
import PlanoMsApi from "@services/PlanoMs/PlanoMsApi";
import PlanoFactory from "@factories/PlanoFactory";

describe("Valida o fluxo de endpoints da agenda", () => {
    let codigoAula: string,
        matricula: string,
        codigoPlano: any,
        codigoAluno: any;
    const usuario = EnvUtils.usuarioPactoBr();
    const data = DateUtils.currenteDateBR();

    before(() => {
        ApiAutenticacao.token(usuario, "teste").then((response) => {
            const token = response.body.content.token;

            AdmMsClientesApi.incluirClienteSemLogar(token, FANTASIA).then(() => {
                codigoAluno = Cypress.env("codigoAluno");
                matricula = Cypress.env("response").matricula;

                ZwBootApi.gravarSemLogar(token, FANTASIA, 6, 2, codigoAluno);
            });
            const plano = PlanoFactory.planoDeTurma();
            PlanoMsApi.incluirPlanoSemLogar(token, FANTASIA, plano).then((response) => {
                codigoPlano = Number(response.body.content.codigo);
                ZwBootApi.gravarPlanoTurma(token, FANTASIA, 2, codigoAluno, codigoPlano, data);
            });
        });
    });

    it("deve consultar aulas com sucesso", () => {
        AppTreinoApi.consultarAulas(data, FANTASIA.codigo, matricula, true).then((response) => {
            codigoAula = JSON.stringify(response.body.aulas[0].codigo);
        });
    });

    it("aluno ativo deve marcar aula coletiva com sucesso", () => {
        AppTreinoApi.marcarAulaColetiva(data, matricula, codigoAula, false, "APP_TREINO");
    });

    it("deve retornar {sucesso} ao consultar aluno presente na aula", () => {
        AppTreinoApi.confirmarAlunoAula(codigoAula, "32", data);
    });

    it("deve retornar os alunos da aula com sucesso", () => {
        AppTreinoApi.consultarAlunosDeUmaAula(codigoAula, data);
    });

    it("deve desmarcar aula com sucesso", () => {
        AppTreinoApi.desmarcarAulaColetiva(data, matricula, codigoAula);
    });

    it("deve consultar saldo de aulas coletivas com sucesso", () => {
        AppTreinoApi.consultarSaldoDeAulasColetivas(matricula).then((response) => {
            AppTreinoApi.validarResponse(response, 200, "sucesso");
        });
    });

    // it("deve consultar histórico de aulas com sucesso", () => {
    //     const dataInicio = DateUtils.currenteDateBR();
    //     const dataFim = DateUtils.currenteDateBR();
    //
    //     AppTreinoApi.consultarHistoricoDeAulas(matricula, dataInicio, dataFim, codigoPlano).then((response) => {
    //         AppTreinoApi.validarResponse(response, 200, "nomeaula");
    //     });
    // });


});
