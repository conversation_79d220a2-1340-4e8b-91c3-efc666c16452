const ConfigurarAulaComReservaEquipamentosLocators = {
  moduloAgenda: '#module-agn',
  cadastros: '#sidebar-item-cadastros > .pacto-item-menu > .pacto-item-menu-content',
  configurarAulas: '#sidebar-item-configurar-aula > [data-cy="AGENDA_CONFIGURAR_AULAS"]',
  inputNomeAula: '#nome-aula-input',
  selectModalidade: '#modalidade-select',
  selectProfessor: '#professor-select',
  selectAmbiente: '#ambiente-select',
  inputTolerancia: '#tolerancia-aula-input',
  selectTipoTolerancia: '#tipotolerancia-select',
  inputCapacidade: '#capacidade-aula-input',
  checkSegunda: '#segunda-check',
  inputHorario: '#horario-aula-input',
  botaoAdicionarHorario: '#btn-add-horario',
  inputDataInicio: '#data-inicio-input',
  inputDataFinal: '#data-fim-input',
  selectReservaEquipamento: '#reserva-equipamento',
  selectEquipamento: '#aparelho-reserva-equipamento-select',
  botaoSalvarAula: '#btn-add-aula',
  aparelhos: '[data-cy="TREINO_APARELHOS"]',
  botaoNovoAparelho: '#btn-novo-aparelho',
  inputNomeAparelho: '#nome-aparelho-input',
  inputSiglaAparelho: '#sigla-aparelho-input',
  buttonAdicionarIcone: '#btnAddIconAparelho',
  primeiroIcone: ':nth-child(1) > .image-wrapper',
  buttonSelecionarIcone: '#selectImagem',
  buttonSalvarAparelho: '#btn-add-aparelho'
}

export default ConfigurarAulaComReservaEquipamentosLocators;