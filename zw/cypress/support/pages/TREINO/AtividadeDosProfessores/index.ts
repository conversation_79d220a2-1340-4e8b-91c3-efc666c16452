import { ELEMENTS as el } from "./elements";
const PIPE = Cypress.config("baseUrl").replace(/.*\/\/(\d+)-.*/, "$1");

class AtividadeDosProfessores {
  validaNovosTreinos(cliente: string) {
    cy.intercept("POST", "**/share-ms/share**").as("share").wait(1000);

    cy.get(el.moduloTreino, { timeout: 10000 }).should("be.visible").click();
    cy.get("#sidebar-menu-toggle > .pct").click();
    cy.get(el.menuRelatorios, { timeout: 10000 }).should("be.visible").click();
    cy.get(el.atividadeDosProfessores, { timeout: 10000 })
      .should("be.visible")
      .click();
    cy.get(el.novos, { timeout: 10000 }).click();
    cy.contains(cliente).should("be.visible");
    cy.get(el.compartilharModal, { timeout: 10000 }).click();
    cy.get(el.copiarLinkModal, { timeout: 10000 }).click();

    cy.wait("@share").then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
      cy.log("Link copiado com sucesso");
    });

    cy.get(el.fechar).click();
  }

  validaRenovados(cliente: string) {
    cy.intercept("POST", "**/share-ms/share**").as("share").wait(1000);

    cy.get(el.moduloTreino, { timeout: 10000 }).should("be.visible").click();
    cy.get("#sidebar-menu-toggle > .pct").click();
    cy.get(el.menuRelatorios, { timeout: 10000 }).should("be.visible").click();
    cy.get(el.atividadeDosProfessores, { timeout: 10000 })
      .should("be.visible")
      .click();

    cy.get(el.renovados, { timeout: 10000 }).click();
    cy.contains(cliente).should("be.visible");
    cy.get(el.compartilharModal, { timeout: 10000 }).click();
    cy.get(el.copiarLinkModal, { timeout: 10000 }).should("be.visible").click();

    cy.wait("@share").then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
      cy.log("Link copiado com sucesso");
    });
    cy.wait(500);

    cy.get(el.fechar).click();
  }

  validaRevisados() {
    cy.intercept("POST", "**/share-ms/share**").as("share");

    cy.get(el.revisados, { timeout: 10000 }).click();
    cy.contains("AT").should("be.visible");
    cy.get(el.compartilharModal, { timeout: 10000 }).click();
    cy.get(el.copiarLinkModal, { timeout: 10000 }).should("be.visible").click();

    cy.wait("@share").then((interception) => {
      expect(interception.response.statusCode).to.eq(200);
      cy.log("Link copiado com sucesso");
    });
    cy.wait(500);

    cy.get(el.fechar).click();
  }

  validaTotais() {
    let somaNovos = 0;
    let somaRenovados = 0;
    let somaRevisados = 0;

    cy.get("table tbody tr")
      .not(":last-child")
      .each(($row) => {
        const colunas = $row.find("td");
        somaNovos += parseInt(colunas.eq(1).text().trim()) || 0;
        somaRenovados += parseInt(colunas.eq(2).text().trim()) || 0;
        somaRevisados += parseInt(colunas.eq(3).text().trim()) || 0;
      });

    cy.get("table tbody tr:last-child").then(($totalRow) => {
      const totalNovos =
        parseInt($totalRow.find("td").eq(1).text().trim()) || 0;
      const totalRenovados =
        parseInt($totalRow.find("td").eq(2).text().trim()) || 0;
      const totalRevisados =
        parseInt($totalRow.find("td").eq(3).text().trim()) || 0;

      expect(somaNovos).to.eq(totalNovos);
      expect(somaRenovados).to.eq(totalRenovados);
      expect(somaRevisados).to.eq(totalRevisados);
    });
  }
}

export default new AtividadeDosProfessores();
