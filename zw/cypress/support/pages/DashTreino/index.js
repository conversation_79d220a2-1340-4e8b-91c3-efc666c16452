import { ELEMENTS as el } from './elements';
import EnvUtils from "../../Kula/utils/EnvUtils";

class DashboardTreino {

    editarVinculoPorAPI(acao) {
        return cy.getCookies().then((cookies) => {
            const pessoaMsUrl = EnvUtils.pessoaMsUrl();

            cy.log(`Alterando vinculos`);
            const token = cookies.find((cookie) => cookie.name === "token").value;
            const pipe = Cypress.config('baseUrl').replace(/.*\/\/(\d+)-.*/, '$1');
            return cy
                .request({
                    method: "POST",
                    url: `${pessoaMsUrl}/alunoColaboradorUsuario/saveOrUpdate`,
                    headers: {
                        'Content-Type': 'application/json',
                        authorization: `Bearer ${token}`,
                    },
                    body: {
                        "abaAcessoCatraca": {
                            "matricula": 70,
                            "codigoPessoa": 83,
                            "codigoCliente": 54
                        },
                        "abaDadosBasicos": {
                            "codigoPessoa": 83,
                            "nome": "BÔNUS RETIRAR DIAS",
                            "fotoBase64OrUrl": null,
                            "dataNasc": "2000-04-28",
                            "tipoDePessoa": [
                                {
                                    "codigo": 1,
                                    "sigla": "CI",
                                    "descricao": "Cliente"
                                }
                            ],
                            "tipoPessoa": 0
                        },
                        "abaDadosFinanceiros": {},
                        "abaVinculos": {
                            "vinculos": acao === 'inserir' ? [
                                {
                                    "codigo": 79,
                                    "tipoVinculo": "CO",
                                    "cliente": 54,
                                    "colaborador": 1,
                                    "nomeColaborador": "PACTO - MÉTODO DE GESTÃO"
                                },
                                {
                                    "tipoVinculo": "TW",
                                    "nomeColaborador": "PACTO - MÉTODO DE GESTÃO",
                                    "edit": false,
                                    "colaborador": 1
                                },
                                {
                                    "tipoVinculo": "PR",
                                    "nomeColaborador": "PLANO CREDITO",
                                    "edit": false,
                                    "colaborador": 12
                                }
                            ] : [
                                {
                                    "codigo": 79,
                                    "tipoVinculo": "CO",
                                    "cliente": 54,
                                    "colaborador": 1,
                                    "nomeColaborador": "PACTO - MÉTODO DE GESTÃO"
                                }
                            ]
                        }
                    },
                    timeout: 60000
                })
                .then((response) => {
                    expect(response.status).to.eq(200);
                    cy.log('Vinculos adicionados com sucesso');
                });
        });
    }

    Indicadores() {
        cy.get(el.ModuloTreino, { timeout: 10000 }).should('be.visible').click();
        cy.get(el.Dashboard, { timeout: 10000 }).should('be.visible').click();
        cy.get(el.TotaldeAlunos, { timeout: 10000 }).scrollIntoView().should('be.visible').click();
        cy.get(el.Fechar).click({ force: true });
        cy.get(el.AlunosInativos, { timeout: 10000 }).should('be.visible').click();
        cy.get(el.BotaoFecharAtualizado, { timeout: 10000 }).should('be.visible').click();
        cy.get(el.AlunosSemAcompanhamento, { timeout: 10000 }).should('be.visible').click();
        cy.get(el.BotaoFecharAtualizado).should('be.visible').click({ force: true });
    }

    validaFiltroProfessor() {
        this.editarVinculoPorAPI('inserir');
        cy.get(el.atualizarBI, { timeout: 50000 }).scrollIntoView().should('be.visible').click();
        cy.get(el.filtroBIProfessor, { timeout: 50000 }).find('option').then((options) => {
            const professores = [...options].map((option) => option.textContent.trim());
            expect(professores).to.include('PACTO - MÉTODO DE GESTÃO');
            expect(professores).to.not.include('PLANO CREDITO');
        });
        this.editarVinculoPorAPI('remover');
    }
}
export default new DashboardTreino();
