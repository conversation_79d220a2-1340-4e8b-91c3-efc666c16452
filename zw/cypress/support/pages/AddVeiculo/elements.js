export const adicionarVeiculoElements = {
    moduloNTR: '[data-cy="modulo-sigla-NTR"]',
    campoBusca: '#topbar-search-field',
    btnDadosPessoais: '#pch-link-dados-pessoais',
    tabAcessoCatraca: '#cli-tab-acesso-catraca',
    inputPlaca: '#input-text-table-familiar-placa',
    inputDescricao: '#input-text-table-familiar-descricaoVeiculo',
    btnConfirmar: '#element-0-confirm-table-familiar',
    btnLog: '#pch-btn-log > .content',
    logAtividade: 'pacto-log-atividade.ng-star-inserted > pacto-cat-layout-v2 > .pct-content',

     //Cadastro de vistante


     addCliente: '#form\\:linkaddClienteMenuSuperiorZWUI > .pct',
     consulta: '#form\\:termosConsulta',
     consultaCliente: '#form\\:btnConsultarCliente', 
     novo: '#form\\:btnNovo',
     salvar: '[id="form:salvar"]',
     consultor: '#form\\:consultor',
     visitante: '#form\\:cadastrarVisitante2',
     salvarSim: '#formGravarBV\\:btnSimCadastrarVisitante',
     moduloNTR:'[data-cy="modulo-sigla-NTR"]',
     lupa: '#topbar-search-field'

     //acessar Tela Aluno

};
