import { PESSOAS_ELEMENTS } from "./pessoasElements";

export class PerfilAlunoPage {
  static visitarClientes() {
    cy.visit("faces/clientes.jsp");
  }

  static abrirMenuClientes() {
    cy.get(PESSOAS_ELEMENTS.LINK_CLIENTES_MENU_SUPERIOR, { timeout: 70000 })
      .should("be.visible")
      .click();
  }

  static consultarCliente(nome) {
    cy.get(PESSOAS_ELEMENTS.VALOR_CONSULTA, { timeout: 70000 })
      .should("be.visible")
      .type(nome);
    cy.get(PESSOAS_ELEMENTS.CONSULTAR_BUTTON, { timeout: 70000 })
      .should("be.visible")
      .click();
    cy.intercept("POST", "/ZillyonWeb/faces/clientes.jsp*").as(
      "clientesRequest"
    );
  }

  static selecionarCliente() {
    cy.get(PESSOAS_ELEMENTS.NOME_CLIENTE, { timeout: 70000 })
      .parents("a")
      .should("be.visible")
      .invoke("click");
  }

  static selecionarContrato() {
    cy.get(PESSOAS_ELEMENTS.CONTRATO_LINKTN, { timeout: 70000 })
      .should("be.visible")
      .click({ force: true });

    cy.wait(500);
  }

  static selecionarMatricula() {
    cy.get(PESSOAS_ELEMENTS.CODIGO_MATRICULA_LINK)
      .should("be.visible")
      .click({ force: true });
  }

  static abrirAfastamento() {
    cy.url().then((originalUrl) => {
      cy.window().then((win) => {
        cy.stub(win, "open").callsFake((url) => {
          if (url && !url.includes("undefined")) {
            win.location.href = url;
            return {
              close: () => {
                win.focus();
              },
            };
          }
        });
      });
      cy.get(PESSOAS_ELEMENTS.AFASTAMENTO_LINKTN, { timeout: 70000 })
        .should("be.visible")
        .click();
      cy.get(PESSOAS_ELEMENTS.AFASTAMENTO_LINK, { timeout: 70000 })
        .should("be.visible")
        .click({ force: true });
    });
  }

  static estornarAfastamento() {
    cy.get(PESSOAS_ELEMENTS.ESTORNAR_OPERACAONT)
      .should("be.visible")
      .click({ force: true });
    cy.get(PESSOAS_ELEMENTS.SENHA_INPUTNT).should("be.visible").type("123");
    cy.get(PESSOAS_ELEMENTS.CONFIRMAR).should("be.visible").click();
    cy.contains("Estorno realizado com sucesso!", { timeout: 10000 }).should(
      "be.visible"
    );
  }

  static preencherAtestado(dataInicial, dataFinal) {
    cy.get(PESSOAS_ELEMENTS.DATA_TERMINO, { timeout: 70000 })
      .should("be.visible")
      .invoke("click");
    cy.wait(2000);

    cy.get(PESSOAS_ELEMENTS.DATA_INICIO, { timeout: 70000 })
      .should("be.visible")
      .type(dataInicial);
    cy.wait(2000);

    cy.get(PESSOAS_ELEMENTS.JUSTIFICATIVA, { timeout: 70000 })
      .should("be.visible")
      .select("4");
    cy.wait(2000);

    cy.get(PESSOAS_ELEMENTS.DATA_TERMINO, { timeout: 70000 })
      .should("be.visible")
      .type(dataFinal);
    cy.wait(2000);
  }

  static confirmarAtestado(senha = false) {
    cy.get(PESSOAS_ELEMENTS.CONFIRMAR_BUTTON, { timeout: 70000 })
      .should("be.visible")
      .click({ force: true });
    cy.wait(4000);

    if (senha) {
      cy.get(PESSOAS_ELEMENTS.SENHA_INPUT).type("123").wait(2000);
      cy.get(PESSOAS_ELEMENTS.AUTORIZAR_BUTTON).click();
      cy.contains("Operação Realizada com sucesso!");
    }
  }

  static validarEstorno() {
    cy.contains("Estorno realizado com sucesso!", { timeout: 10000 }).should(
      "be.visible"
    );
  }

  static abrirFinanceiroContrato() {
    cy.get(PESSOAS_ELEMENTS.VER_MAIS_FINANCEIRO_CONTRATO).click();
  }

  static estornarOperacao() {
    cy.waitVisible(PESSOAS_ELEMENTS.ESTORNAR_OPERACAO);
    cy.get(PESSOAS_ELEMENTS.ESTORNAR_OPERACAO, { timeout: 70000 })
      .should("be.visible")
      .click({ force: true });
    cy.get(PESSOAS_ELEMENTS.SENHA_INPUT).should("be.visible").type("123");
    cy.get(PESSOAS_ELEMENTS.AUTORIZAR_BUTTON).should("be.visible").click();
  }

  static validarMensagemEstorno() {
    cy.contains("Estorno realizado com sucesso", { timeout: 10000 })
      .should("be.visible")
      .and("have.class", "growl-box-message");
  }

  static verificarVigencia() {
    cy.get(PESSOAS_ELEMENTS.CONTRATO_LINK).should("have.text", "31/10/2027");
  }
}
