import DateUtils from "@utils/DateUtils";
import * as faker from "faker-br";

class DisponibilidadeServicoPage {
  elements = {
    NOME_SERVICO: '[data-cy="nome-disponibilidade-input-input"]',
    INTERVALO_FALTAS: "#intervaloMinimoFalta",
    DESCRICAO: '[data-cy="descricao-input"]',
    AVANCAR_INFORMACOES: "#btn-add-aula1",
    SELECT_PROFESSOR: "#professor-select",
    SELECT_AMBIENTE: "#ambiente-select",
    HORARIO_INICIAL:
      ':nth-child(1) > pacto-cat-form-input > .aux-wrapper > [data-cy="input-horaInicial-input"]',
    HORARIO_FINAL:
      '.row.margin-top-40 > :nth-child(2) > pacto-cat-form-input > .aux-wrapper > [data-cy="input-horaFinal-input"]',
    CHECK_TODOS_DIAS:
      ".col-md-2 > pacto-cat-checkbox > .cat-input-wrapper > label > .text",
    SALVAR_HORARIO: '[data-cy="btn-salvar"]',
    OPTIONS_PROFESSOR: "#professor-select > .options > .scroll-container",
    OPTIONS_AMBIENTE: "#ambiente-select > .options > .scroll-container",
    CONCLUIR_DISPONIBILIDADE: "#btn-add-aula3",
    DATA_INICIAL:
      ':nth-child(1) > .datePicker > .icon-wrapper > [data-cy="datepicker-input-label"]',
    DATA_FINAL:
      ':nth-child(2) > .datePicker > .icon-wrapper > [data-cy="datepicker-input-label"]',
    INTERVALO_DIAS_AGENDAMENTO: "#intervaloDiasAgendamento",
  };

  criarDisponibilidadeIntervaloDeFaltas(qtdDias: string): void {
    const nomeServico = faker.name.findName();
    cy.contains("button", "Cadastrar disponibilidade").click();
    // Aba Informações
    cy.get(this.elements.NOME_SERVICO).type(nomeServico);
    cy.get(this.elements.INTERVALO_FALTAS).type(qtdDias);
    cy.get(this.elements.DESCRICAO).type(nomeServico);
    cy.get(this.elements.AVANCAR_INFORMACOES).click();
    // Aba Data e Horários
    cy.get(this.elements.DATA_INICIAL).clear().type(DateUtils.futureDateBR(-5));
    cy.get(this.elements.DATA_FINAL).clear().type(DateUtils.futureDateBR(30));
    cy.contains("button", "Adicionar horário").click();
    cy.get(this.elements.SELECT_PROFESSOR).click();
    cy.get(this.elements.OPTIONS_PROFESSOR)
      .contains("ADRIANA COSTA")
      .should("exist")
      .click({ force: true });
    cy.get(this.elements.SELECT_AMBIENTE).click();
    cy.get(this.elements.OPTIONS_AMBIENTE)
      .contains("TESTE AUTO")
      .should("exist")
      .click({ force: true });
    cy.get(this.elements.HORARIO_INICIAL).type("0800");
    cy.get(this.elements.HORARIO_FINAL).type("0900");
    cy.get(this.elements.CHECK_TODOS_DIAS).click();
    cy.get(this.elements.SALVAR_HORARIO).click();
    cy.get(this.elements.CONCLUIR_DISPONIBILIDADE).click();
    cy.contains("Disponibilidade criada com sucesso.", {
      timeout: 20000,
    }).should("be.visible");
  }

  criarDisponibilidadeComIntervalo(qtdDias: string): Cypress.Chainable {
    const nomeServico = faker.name.findName();
    cy.contains("button", "Cadastrar disponibilidade").click();
    // Aba Informações
    cy.get(this.elements.NOME_SERVICO).type(nomeServico);
    cy.get(this.elements.INTERVALO_DIAS_AGENDAMENTO).type(qtdDias);
    cy.get(this.elements.DESCRICAO).type(nomeServico);
    cy.get(this.elements.AVANCAR_INFORMACOES).click();
    // Aba Data e Horários
    cy.get(this.elements.DATA_INICIAL).clear().type(DateUtils.futureDateBR(-5));
    cy.get(this.elements.DATA_FINAL).clear().type(DateUtils.futureDateBR(30));
    cy.contains("button", "Adicionar horário").click();
    cy.get(this.elements.SELECT_PROFESSOR).click();
    cy.get(this.elements.OPTIONS_PROFESSOR)
      .contains("PACTO - MÉTODO DE GESTÃO")
      .should("exist")
      .click({ force: true });
    cy.get(this.elements.SELECT_AMBIENTE).click();
    cy.get(this.elements.OPTIONS_AMBIENTE)
      .contains("TESTE AUTO")
      .should("exist")
      .click({ force: true });
    cy.get(this.elements.HORARIO_INICIAL).type("0100");
    cy.get(this.elements.HORARIO_FINAL).type("0200");
    cy.get(this.elements.CHECK_TODOS_DIAS).click();
    cy.get(this.elements.SALVAR_HORARIO).click();
    cy.get(this.elements.CONCLUIR_DISPONIBILIDADE).click();
    cy.contains("Disponibilidade criada com sucesso.", {
      timeout: 20000,
    }).should("be.visible");
    return cy.wrap(nomeServico);
  }
}

export default new DisponibilidadeServicoPage();
