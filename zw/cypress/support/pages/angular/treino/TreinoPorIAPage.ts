class TreinoPorIAPage {
  elements = {
    CHECK_RES_NAO: '#res-nao',
    INPUT_ATURA: 'input[formcontrolname="alturaAluno"]',
    INPUT_PESO: 'input[formcontrolname="pesoAluno"]'

  }

  validarPreenchimentoAutomaticoAnamnese(): void {
    cy.get(this.elements.CHECK_RES_NAO).check()
    cy.contains('button', 'Avançar').should('not.be.disabled').click()
    cy.get(this.elements.INPUT_ATURA).should('have.value', '182')
    cy.get(this.elements.INPUT_PESO).should('have.value', '99')
  }
}

export default new TreinoPorIAPage()