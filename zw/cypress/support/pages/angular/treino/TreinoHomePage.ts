import * as faker from 'faker-br'

class TreinoHomePage {
  elements = {
    SELECT_MODAL: 'ds3-select',
    OPTION_SELECT_MODAL: '.ds3-select-options',
    ICONE_CALENDARIO: '.avisos-modal-form .pct-calendar',
    DATA_ATUAL: '.mat-calendar-body-active > .mat-calendar-body-cell-content',
    INPUT_MENSAGEM: '.avisos-modal-form textarea',
    AVISOS: '.avisos',
    BOTAO_ACOES: '.avisos-item .dropdown-toggle',
    SELECT_PESSOAS: '.avisos-modal-form ds3-select-multi',
    TITULO_MODAL: '.modal-titulo'
  }

  adicionarAvisoParaTodos(sentenca: string): void {
    cy.wait(2000)
    cy.contains('button', 'Adicionar novo aviso', { timeout: 10000 }).click()
    cy.get(this.elements.SELECT_MODAL).first().click()
    cy.get(this.elements.OPTION_SELECT_MODAL).should('exist').contains('Todos da empresa').click()
    cy.get(this.elements.ICONE_CALENDARIO).click()
    cy.get(this.elements.DATA_ATUAL).click()
    cy.get(this.elements.INPUT_MENSAGEM).type(sentenca)
    cy.contains('button', 'Publicar aviso').click()
    cy.contains('Aviso salvo com sucesso.').should('be.visible')
  }

  adicionarAvisoParaPessoasEspecificas(sentenca: string): void {
    cy.wait(5000)
    cy.contains('button', 'Adicionar novo aviso', { timeout: 10000 }).click()
    cy.get(this.elements.SELECT_MODAL).first().click()
    cy.get(this.elements.OPTION_SELECT_MODAL).should('exist').contains('Pessoas específicas').click()
    cy.get(this.elements.ICONE_CALENDARIO).click()
    cy.get(this.elements.DATA_ATUAL).click()
    cy.get(this.elements.SELECT_PESSOAS).click()
    cy.get(this.elements.OPTION_SELECT_MODAL).should('exist').contains('ENZO GENIN').click()
    cy.get(this.elements.TITULO_MODAL).click().wait(3000)
    cy.get(this.elements.INPUT_MENSAGEM).type(sentenca)
    cy.contains('button', 'Publicar aviso').click()
    cy.contains('Aviso salvo com sucesso.').should('be.visible')
  }

  adicionarAvisoParaPerfilAcesso(sentenca: string) {
    cy.wait(5000)
    cy.contains('button', 'Adicionar novo aviso', { timeout: 10000 }).click()
    cy.get(this.elements.SELECT_MODAL).first().click()
    cy.get(this.elements.OPTION_SELECT_MODAL).should('exist').contains('Perfil de acesso específico').click()
    cy.get(this.elements.ICONE_CALENDARIO).click()
    cy.get(this.elements.DATA_ATUAL).click()
    cy.get(this.elements.SELECT_PESSOAS).click()
    cy.get(this.elements.OPTION_SELECT_MODAL).should('exist').contains('COORDENADOR').click()
    cy.get(this.elements.TITULO_MODAL).click().wait(3000)
    cy.get(this.elements.INPUT_MENSAGEM).type(sentenca)
    cy.contains('button', 'Publicar aviso').click()
    cy.contains('Aviso salvo com sucesso.').should('be.visible')
  }

  visualizarAviso(sentenca: string): void {
    cy.wait(5000)
    cy.get(this.elements.AVISOS, { timeout: 10000 }).contains(sentenca)
  }

  excluirAviso(): void {
    cy.wait(2000)
    cy.get(this.elements.BOTAO_ACOES).first().click()
    cy.contains('button', 'Excluir aviso').first().click()
    cy.contains('Aviso apagado com sucesso.').should('be.visible')
  }
}

export default new TreinoHomePage();