class AlunosFaltososPage {

  elements = {
    BOTAO_CONFIGURAR_BLOQUEIO: '#btn-Config-DiasLabel',
    QTD_FALTAS_BLOQUEIO: ':nth-child(1) > pacto-input-number > .form-group > #tolerancia-aula-input',
    TEMPO_BLOQUEIO: ':nth-child(2) > pacto-input-number > .form-group > #tolerancia-aula-input',
    BOTAO_CADASTRAR_BLOQUEIO: '#gravarCadastroNivel',
    FILTROS: '#filtros-dropdown',
    SESSAO_DATA_INICIO: '#filter-title-dataInicio',
    INPUT_DATA_INICIO: '#filter-option-date-dataInicio-input',
    SESSAO_DATA_FIM: '#filter-title-dataFim',
    INPUT_DATA_FIM: '#filter-option-date-dataFim-input',
    BUSCAR: '#filter-search-button',
    TABELA_ALUNOS: '.table-content'
  }

  configurarDiasBloqueio(qtdFaltas: string, diasBloqueio: string): void {
    cy.get(this.elements.BOTAO_CONFIGURAR_BLOQUEIO, { timeout: 10000 }).click()
    cy.get(this.elements.QTD_FALTAS_BLOQUEIO).clear().type(qtdFaltas)
    cy.get(this.elements.TEMPO_BLOQUEIO).clear().type(diasBloqueio)
    cy.get(this.elements.BOTAO_CADASTRAR_BLOQUEIO).type(diasBloqueio)
    cy.contains('Configuração registrada com sucesso.').should('be.visible')
  }

  filtrarFaltasPorData(dataInicio: string, dataFinal: string): void {
    cy.get(this.elements.FILTROS).click()
    cy.get(this.elements.SESSAO_DATA_INICIO).click()
    cy.get(this.elements.INPUT_DATA_INICIO).type(dataInicio)
    cy.get(this.elements.SESSAO_DATA_FIM).click()
    cy.get(this.elements.INPUT_DATA_FIM).type(dataInicio)
    cy.get(this.elements.BUSCAR).click()
  }

  validarAlunoFaltoso(nomeAluno: string): void {
    cy.get(this.elements.BUSCAR).type(nomeAluno)
    cy.get(this.elements.TABELA_ALUNOS).contains(nomeAluno)
  }

}

export default new AlunosFaltososPage()