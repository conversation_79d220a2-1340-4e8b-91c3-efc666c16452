import * as faker from 'faker-br'

class CadastroCartaoPage {
  private static readonly TIMEOUT = 10000;
  private static readonly elements = {
    NOME_TITULAR: '#idnomecartao',
    NUMERO_CARTAO: '#idnrcartao',
    VALIDADE: '#idvalidade',
    CODIGO_SEGURANCA: '#idcvv',
    CHECK_LGPD: ':nth-child(5) > .col-12 > pacto-input > .form-group > .form-control',
    ADICIONAR_CARTAO: '.pacto-btn-primary',
  }

  cadastrarCartao(numeroCartao: string, vencimentoCartao: string, cvv: string) {
    cy.get(CadastroCartaoPage.elements.NOME_TITULAR, { timeout: CadastroCartaoPage.TIMEOUT }).type(faker.name.findName())
    cy.get(CadastroCartaoPage.elements.NUMERO_CARTAO).type(numeroCartao)
    cy.get(CadastroCartaoPage.elements.VALIDADE).type(vencimentoCartao)
    cy.get(CadastroCartaoPage.elements.CODIGO_SEGURANCA).type(cvv)
    cy.get(CadastroCartaoPage.elements.CHECK_LGPD).click()
    cy.get(CadastroCartaoPage.elements.ADICIONAR_CARTAO).click()
  }

}

export default new CadastroCartaoPage();