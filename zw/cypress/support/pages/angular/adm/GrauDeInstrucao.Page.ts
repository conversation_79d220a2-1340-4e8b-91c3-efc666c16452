const { faker } = require('@faker-js/faker');

class GrauDeInstrucaoPage {

    elements = {

        ADICIONAR: '#btn-add-item',
        NOME: '[title="Doutorado"]',
        BUSCA: '#input-busca-rapida',
        LISTA: '#element-0 > :nth-child(2)',
        EXCLUIR: '#element-0-action-delete\\ \\(key\\)'

    }
    adicionarGrauInstrucao() {
        const nomeGRAU = faker.name.jobTitle(); 

        cy.get(this.elements.ADICIONAR).click();
        cy.get(this.elements.NOME).type(nomeGRAU);

        cy.contains('Salvar').should('be.visible').click();
        cy.contains('Grau de instrução cadastrado com sucesso!').should('be.visible');

        return cy.wrap(nomeGRAU); 
    }

    editarGrauInstrucao(nomeGRAU) {
        cy.intercept('GET', '**/grau-instrucao?filters=*').as('buscarGrau');

        cy.get(this.elements.BUSCA).type(nomeGRAU);
        cy.wait(500);
        cy.wait('@buscarGrau');

        cy.get(this.elements.LISTA).should('be.visible').click();

        const novoNomeGRAU = faker.name.jobTitle(); 
        cy.get(this.elements.NOME).clear().type(novoNomeGRAU);

        cy.contains('Salvar').should('be.visible').click();
        cy.contains('Grau de instrução cadastrado com sucesso!').should('be.visible');

        return cy.wrap(novoNomeGRAU); 
    }


    excluirGrauInstrucao(nomeGRAU) {
        cy.intercept('GET', '**/grau-instrucao?filters=*').as('buscarGrau');

        cy.get(this.elements.BUSCA).clear().type(nomeGRAU);
        cy.wait(500);
        cy.wait('@buscarGrau');

        cy.contains(nomeGRAU, { timeout: 10000 }).should('be.visible')
        cy.get(this.elements.EXCLUIR, { timeout: 15000 }).should('be.visible').click();

        cy.contains('Grau de instrução excluído com sucesso.').should('be.visible');
    }
}


export default new GrauDeInstrucaoPage();