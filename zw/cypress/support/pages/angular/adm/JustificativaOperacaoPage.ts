class JustificativaOperacaoPage {
    elements = {

        BOTAO_ADICIONAR: '#btn-add-item',
        DESCRICAO: '.aux-wrapper > #descricao',
        TIPO_OPERACAO: '.aux-parent > #tipo-operacao',
        BOTAO_SALVAR: '#btn-salvar > .content',
        BUSCA: '#input-busca-rapida',
        TABELA_RELATORIO: '.table-content',
        BOTAO_EXCLUIR: '#element-0-action-delete',
        MODAL_EXCLUIR: '.pacto-modal-wrapper',
        MODAL_BOTAO_EXCLUIR: '#btn-action-modal > .content'

    }

    cadastrarJustificativaOperacao(descricao: string): void {
        cy.wait(3000)
        cy.get(this.elements.BOTAO_ADICIONAR).should('be.visible').click()
        cy.get(this.elements.DESCRICAO, {timeout: 15000}).should('be.visible').type(descricao)
        cy.get(this.elements.TIPO_OPERACAO).should('be.visible').select('Trancamento')
        cy.get(this.elements.BOTAO_SALVAR).should('be.visible').click()


    }

    buscarJustificativaOperacao(descricao: string): void {
        cy.wait(3000)
        cy.get(this.elements.BUSCA, {timeout: 15000}).should('be.visible').type(descricao)
        cy.get(this.elements.TABELA_RELATORIO, {timeout: 15000}).should('be.visible').should('contain', descricao)

    }

    excluirJustificativaOperacao(descricao: string): void {
        cy.wait(3000)
        cy.get(this.elements.BOTAO_EXCLUIR, {timeout: 15000}).should('be.visible').click()
        cy.get(this.elements.MODAL_EXCLUIR, {timeout: 15000}).should('be.visible')
        cy.get(this.elements.MODAL_BOTAO_EXCLUIR, {timeout: 15000}).should('be.visible').click()
        cy.get(this.elements.TABELA_RELATORIO, {timeout: 15000}).should('be.visible').should('not.contain', descricao);
    }

}


export default new JustificativaOperacaoPage();
