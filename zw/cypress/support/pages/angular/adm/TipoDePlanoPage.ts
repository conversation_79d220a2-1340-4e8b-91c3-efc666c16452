class TipoDePlanoPage {
    elements = {
        ADICIONAR:'#btn-add-item',
        NOME:'[title="Nome"]',
        TIPO:'[title="Tipo"]',
        ATIVO: '.checkmark-outline',
        BUSCA: '#input-busca-rapida',

    }

    adicionarTipoPlano() {
        cy.get(this.elements.ADICIONAR).click()
        cy.get(this.elements.NOME).click().type('teste auto');
        cy.get(this.elements.TIPO).click().type('QA');
        cy.get(this.elements.ATIVO).click()
        cy.contains('Salvar').click()
        cy.contains('Tipo De Plano cadastrado com sucesso').should('be.visible')

 }
  editarTipoPlano() {

    cy.get(this.elements.BUSCA, { timeout: 15000 })
        .should('be.visible')
        .click()
        .type('teste auto');

        cy.contains('teste auto').click();
        cy.get(this.elements.NOME, { timeout: 15000 }).clear().click().type('teste auto 02');

        cy.contains('Salvar').click()
        cy.contains('Tipo De Plano cadastrado com sucesso').should('be.visible')



}
}


    export default new TipoDePlanoPage();