class CondicaoPagamento {
    elements = {
        BUSCA: '#input-busca-rapida-condicao-pagamento',
        RESULTADO_BUSCA: '#element-0-condicao-pagamento',
        TIPO_CONVENIO_COBRANCA: '[data-cy="nova-condicao-pagamento-tipo-convenio-cobranca"]',
        RECORRENCIA: '#nova-condicao-pagamento-recorrencia > .cat-input-wrapper > label > .text',
        SALVAR:'[data-cy="nova-condicao-pagamento-btn-salvar"] > .content'
    }

    adicionarTipoConvenio(): void {
        cy.intercept('GET', '**/condicoesPagamento?filters=**').as('filtroCondicao');

        cy.get(this.elements.BUSCA)
          .click()
          .type('IN 12 TIMES - RECURRENCE CARD');
        
        cy.wait('@filtroCondicao')
          .its('response.statusCode')
          .should('eq', 200);
      
        cy.get(this.elements.RESULTADO_BUSCA, { timeout: 15000 }).click();
      
        cy.get(this.elements.TIPO_CONVENIO_COBRANCA, { timeout: 15000 })
          .select('DCC Stone Online');
      
        cy.get(this.elements.RECORRENCIA).click();
      
        cy.get(this.elements.SALVAR, { timeout: 15000 }).click();
      
        cy.contains('Condicão de pagamento salva com sucesso!', { timeout: 15000 })
          .should('be.visible');
    }
}

export default new CondicaoPagamento();
