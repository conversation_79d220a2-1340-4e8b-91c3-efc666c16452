class ContaCorrentePage {

    agencia = null;
    novaAgencia = null;

    elements = {
        ADICIONAR: '#btn-add-item',
        AGENCIA: 'input[title="0"]',
        BUSCA: '#input-busca-rapida',
        EXCLUIR: 'i.pct-trash-2.cor-action-default-risk04',
    }

    gerarNumeroAleatorio(digitos) {
        const min = Math.pow(10, digitos - 1);
        const max = Math.pow(10, digitos) - 1;
        return Math.floor(min + Math.random() * (max - min + 1)).toString();
    }

    cadastrarContaCorrente() {
        this.agencia = this.gerarNumeroAleatorio(4);
        const digitoAgencia = this.gerarNumeroAleatorio(1);
        const conta = this.gerarNumeroAleatorio(4);
        const digitoConta = this.gerarNumeroAleatorio(1);

        cy.intercept('POST', '**/contasCorrentes').as('criarContaCorrente');

        cy.get(this.elements.ADICIONAR).click();
        cy.get(this.elements.AGENCIA).eq(0).type(this.agencia);
        cy.contains('Dígito verificador da agência')
            .parents('pacto-cat-form-input')
            .find('input')
            .type(digitoAgencia);
        cy.get('.aux-wrapper input').eq(3).type(conta);
        cy.get('.aux-wrapper input').eq(4).type(digitoConta);
        cy.get('.current-value').click();
        cy.contains('CREDIREAL').click();

        cy.contains('Salvar').click();

        cy.contains('Conta corrente salva com sucesso!').should('be.visible');

        cy.wait('@criarContaCorrente').its('response.statusCode').should('eq', 200);
    }

    editarContaCorrente() {
        this.novaAgencia = this.gerarNumeroAleatorio(4);

        cy.intercept('GET', '**/contasCorrentes*').as('buscarContaCorrente');

        cy.get(this.elements.BUSCA).clear().type('CREDIREAL');

        cy.wait('@buscarContaCorrente').its('response.statusCode').should('eq', 200);


        cy.contains(this.agencia, { timeout: 3000 })
            .should('be.visible')
            .click();

        cy.get(this.elements.AGENCIA).eq(0)
            .clear()
            .type(this.novaAgencia);


        cy.contains('Salvar').click();
        cy.contains('Conta corrente salva com sucesso!').should('be.visible');

    }

    excluirContaCorrente() {
        cy.intercept('DELETE', '**/contasCorrentes/*').as('excluirContaCorrente');

        cy.contains('tr', this.novaAgencia, { timeout: 3000 })
            .should('be.visible')
            .within(() => {
                cy.get(this.elements.EXCLUIR).click();
            });

        cy.wait('@excluirContaCorrente').its('response.statusCode').should('eq', 200);

        cy.get(this.elements.BUSCA).clear().type('CREDIREAL');
        cy.contains(this.novaAgencia).should('not.exist');
    }
}


export default new ContaCorrentePage();