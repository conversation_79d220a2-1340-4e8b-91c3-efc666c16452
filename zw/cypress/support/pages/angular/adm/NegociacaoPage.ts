import EnvUtils from "@utils/EnvUtils";
import MenuSuperiorPage from "./MenuSuperiorPage";
import { ModulosEnum } from "../modulos-enum";
import { ClientePessoa } from "@models/Cliente";
import DateUtils from "@utils/DateUtils";

class NegociacaoPage {
    elements = {
        // Cadastro aluno
        INCLUIR_CLIENTE: "#taskbar-resource-add-person > .pct",
        INPUT_CONSULTA: "#form\\:termosConsulta",
        BOTAO_CONSULTAR: "#form\\:btnConsultarCliente",
        BOTAO_CADASTRAR_NOVO: "#form\\:btnNovo",
        INPUT_DATA_NASCIMENTO: "#form\\:dataNascInputDate",
        MASCULINO: "#form\\:sexo\\:0",
        CONFIRMAR_VISITANTE: "#form\\:salvar",
        CONSULTOR_VISITATE: "#form\\:consultor",
        CADASTRAR_VISITANTE: "#form\\:cadastrarVisitante",
        CONFIRMAR_BV: "#formGravarBV\\:btnSimCadastrarVisitante",


        // Negociação
        BUSCA_RAPIDA: 'input[title="Busca rápida"]',
        LISTA_CLIENTE: ".lista-cliente",
        USUARIO: "adolfomariz",
        SELECT_PLANO: "select#input-plano",
        SELECT_CONDICAO: "#input-condicao",
        BOTAO_RECEBER: "#btn-receber > .content",
        BOTAO_CAIXA_ABERTO: "#btn-caixa-aberto > .content",
        BOTAO_DEIXAR_NO_CAIXA_EM_ABERTO: "#btn-caixa-aberto > .content",
        FORMAS_PAGAMENTO_DINHEIRO:
            "#form\\:opcoesFormasPagamento\\:0\\:movPagamentoEscolhido",
        CONFIRMAR: "#form\\:btnConfirmar",
        PAGAMENTO_EFETUADO_MENSAGEM: "Pagamento Efetuado Com Sucesso",
        ADOLFO: "#form\\:nomeResponsavelPagamentolink",
        CONTRATO: "#form\\:listaContrato\\:0\\:linkTipoOperacaoSelecionarContrato",
        ESTORNAR: "#form\\:linkEstornar > span",
        ESTORNO_CONTRATO: "#form\\:estornar",
        ALUNO: ".zebra-row > :nth-child(1)",
        SENHA: "#aut-input-psw",
        CONFIRMAR_SENHA: "#aut-btn-confirm",
        FECHAR_ABA: "#close-aba",
        CONSULTOR: ".ds3-select-body",
        PACTO: '[ng-reflect-value="1"] > .ds3-option',
        SITUACAO_ATIVO: ".situacao-aluno.primario.at",
        ADICIONAR_ITEM_PACOTE:
            "adm-pacote-modalidades-negociacao > .tabela-editavel-negociacao > .add-row",
        SELECT_PACOTE: "#mat-select-0",
        PACOTE: "#mat-option-0",
        SELECT_MODALIDADE1_PACOTE: "#pacto-modalidade-pacote0",
        SELECT_MODALIDADE2_PACOTE: "#pacto-modalidade-pacote1",
        VALOR_MODALIDADE1_PACOTE: ":nth-child(2) > .valor-modalidade-pacote",
        VALOR_MODALIDADE2_PACOTE: ":nth-child(3) > .valor-modalidade-pacote",
        SENHA_RECEBIMENTO: "#formSenhaAutorizacao\\:senha",
        CONFIRMAR_MODAL_RECEBIMENTO: "#formSenhaAutorizacao\\:btnAutorizar",
        VALOR_TOTAL_NEGOCIACAO: "#info-fluxo > .info > :nth-child(5) > .valores",
        VALOR_TOTAL_PLANO: "#cat-tooltip-trigger-1",
        SELECT_HORARIO: "#input-horario",
        VALOR_MODALIDADE: ".row-modalidades > :nth-child(4)",
        EXCLUIR_MODALIDADE: ".acoes > .pct",
        TABELA_PARCELAS_NEGOCIACAO: "adm-parcelas-negociacao table td",
        SITUACAO: ".situacao-aluno",
        RENOVACAO_CONTRATO_SIM: "#btn-sim > .content",
        DATA_NEGOCIACAO: "#datepicker-input-label",
        SALVAR_NEGOCIACAO: "#btn-salvar > .content",
        COBRAR_MATRICULA_SEPARADAMENTE:
            "#check-matricula-separada > .cat-input-wrapper > label > .checkmark-outline",
        SALVAR_CONFIG_AVANCADA: "#btn-salvar > .content",
        ENVIAR_LINK_PAGAMENTO: "#btn-enviar > .content",
        INICIO: ":nth-child(5) > .column-cell > .ng-star-inserted",
        DIVIDIR_PRODUTO_PARCELAS:
            "#check-dividir-produto > .cat-input-wrapper > label > .checkmark-outline",
        PARCELA_PRO_RATA:
            "adm-parcelas-negociacao.ng-star-inserted > table > :nth-child(1) > :nth-child(1)",
        TABELA_MODALIDADE:
            "adm-pacote-modalidades-negociacao > .tabela-editavel-negociacao",
        BOTAO_RECEBER_NEGOCIACAO: '[data-cy="btn-receber"] > .content',
        INPUT_RECEBER_NEGOCIACAO: '.escolha-cliente > :nth-child(1)',
        PRIMEIRA_BUSCA_NEGOCIACAO: 'table > :nth-child(2) > :nth-child(1)',

        //AutorizaçãoCobrança
        BOLETO: '[for="tab_boleto-bancario"]',
        SELECIONAR_CONVENIO: "#convenio-da-cobranca",
        BOTAO_SALVAR: "#btn-save > .content",
        MENSAGEM_MODAL: ".wrapper > .title",
        AUTORIZACAO_COBRANCA: "#btn-autorizacao-cobranca > .content > .lbl",
        BOTAO_SALVAR_AUTORIZACAO: "#btn-save > .content > .lbl",
        CREDITO: '[for="tab_credito"]',
        NUMERO_CARTAO: "#numero-do-cartao",
        TITULAR_CARTAO: "#nome-do-titular",
        VALIDADE_CARTAO: "#validade",
        CVV_CARTAO: "#cvv",

        AVISO_HORARIOS: ".aviso-horarios",
        NOTIFICACAO: ".snotifyToast__inner",
        FILTRO_PROFESSOR: "#pacto-professor",
        SELECT_PRODUTO: "#select-table-produto-descricao",
        QUANTIDADE_PRODUTO: "#qtd-id-produto-input",
        QUANTIDADE_PRODUTO_AUMENTAR: "#qtd-id-produto-increase",
        SALVAR_PRODUTO: "[id^='element-'][id$='-confirm-table-produto']",
        VALOR_PRODUTO: ".pct-table-body > :nth-child(2) > :nth-child(3)",
        VALOR_TOTAL_PRODUTO: ".pct-table-body > :nth-child(2) > :nth-child(6)",
        EDITAR_HORARIOS: ".coluna-link",
        HORARIO_SEGUNDA_LINHA0: ":nth-child(3) > :nth-child(2) > .slot-turma",
        HORARIO_TERCA_LINHA0: ":nth-child(3) > :nth-child(3) > .slot-turma",
        HORARIO_QUARTA_LINHA0: ":nth-child(3) > :nth-child(4) > .slot-turma",
        HORARIO_QUINTA_LINHA0: ":nth-child(3) > :nth-child(5) > .slot-turma",
        HORARIO_SEXTA_LINHA0: ":nth-child(3) > :nth-child(6) > .slot-turma",
        HORARIO_SABADO_LINHA0: ":nth-child(3) > :nth-child(7) > .slot-turma",
        HORARIO_DOMINGO_LINHA0: ":nth-child(3) > :nth-child(8) > .slot-turma",
        HORARIO_QUARTA: ":nth-child(3) > :nth-child(4) > .slot-turma",
        HORARIO_SEXTA: ":nth-child(4) > :nth-child(6) > .slot-turma",
        CONFIRMAR_HORARIOS:
            '.right > pacto-cat-button.ng-star-inserted > [data-cy="btn-receber"] > .content',
        SELECT_MODALIDADE: ".mat-select-value",
        LUPA_MODALIDADE: "#ds3-field-0",
        TEXTO_MODALIDADE: ".mat-option-text",
        OPCAO_INPUT: "mat-option",

        //EditarPlano
        BUSCA_EDITAR_PLANO: "#input-busca-rapida-plano",
        DATA_EDITAR_PLANO: "#datepicker-ingresso-plano-input",


        //Desconto
        EDITAR_DESCONTO: ".edit-icon > .pct",
        VALOR_TOTAL_DESCONTO: "#info-fluxo > .info > :nth-child(5)",
        DESCONTO:
            "pacto-cat-accordion.ng-tns-c57-2 > .accordion-wrapper > .header-section > div.title-content",
        DESCONTO_TIPO: "#pacto-tipo-desconto",
        CONVENIO_DESCONTO: "#pacto-convenio-desconto",
        INPUT_DESCONTO_PRODUTO: 'input[placeholder="00,00"]',
        ADICIONAR_DESCONTO_PRODUTO: '[data-cy="btn-novo-desconto"]',
        VALOR_DESCONTO: 'input[placeholder="00,00"]',
        VALOR_DESCONTO_PERCENTUAL: 'input[placeholder="0%"]',
        VALOR_FINAL_DESCONTO: "#cat-tooltip-trigger-5",
        ABA_FINANCEIRO: "#pcc-tab-financeiro",
        DESCONTO_PERFIL_DO_ALUNO:
            "#element-0-pch-fin-table-compras > :nth-child(4) > .column-cell",
        DESCONTO_VALOR_CONVENIO:
            "#element-1-pch-fin-table-compras > :nth-child(8) > .column-cell",
        BOTAO_MAIS: ".icon-drop > .pct",

        TIPO_DESCONTO: "#pacto-tipo-desconto",
        DESCONTO_CONVENIO: "div.aux-wrapper.icons-after input",
        VALIDAR_VALOR: "#info-fluxo > .info > :nth-child(5) > .valores",

        PARCELA_NEGOCIACAO:
            "adm-parcelas-negociacao.ng-star-inserted > table > :nth-child(1) > :nth-child(1)",
        CAIXA_ABERTO: "#btn-caixa-aberto-2 > .content > .lbl",
        CARTAO_CREDITO: '[for="tab_credito"]',
        RENOVAR_CONTRATO: '[data-cy="btn-sim"] > .content',
        CALENDARIO_DATA_LANCAMENTO: "#datepicker-input",
        CALENDARIO: ".mat-calendar-content",
        BOTAO_SALVAR_DATA: '[data-cy="btn-salvar"] > .content',
        PRIMEIRA_PARCELA:
            "adm-parcelas-negociacao.ng-star-inserted > table > :nth-child(1) > :nth-child(1)",
        HEADER_NEGOCIACAO: ".content-wrapper",
        LISTA_PRODUTOS_NEGOCIACAO:
            "adm-produtos-negociacao.ng-star-inserted > .tabela-editavel-negociacao",
        CONFIRMAR_RENOVACAO: '[data-cy="btn-sim"] > .content',
        LOADER: ".painelLoader > double-arrow",
        INPUT_DATA_LANCAMENTO: '[data-cy="datepicker-input-label"]',
        INPUT_DATA_INICIO: '[data-cy="cat-datepicker-0-input"]',
    };

    private aguardarGravarContrato(timeout = 60000): void {
        this.interceptarRequisicoes();
        cy.wait("@gravarContrato", { timeout }).then((interception) => {
            if (interception?.response?.statusCode === 200) {
                expect(interception.response.statusCode).to.eq(200);
            } else {
                throw new Error("Falha ao gravar contrato após múltiplas tentativas.");
            }
        });
    }


    receberNegociacao(pessoa) {
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).click({ force: true }).wait(2000)
        cy.get(this.elements.INPUT_RECEBER_NEGOCIACAO).type(pessoa).wait(3000)
        cy.get(this.elements.PRIMEIRA_BUSCA_NEGOCIACAO).click()
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).click().wait(30000)
        cy.get(this.elements.RENOVAR_CONTRATO).click().wait(4000)
        cy.wait(2000)
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).click({ force: true }).wait(32000)
    }

    private aguardarSimularContrato(): void {
        cy.wait("@simularContrato", { timeout: 60000 }).then((interception) => {
            expect(interception?.response?.statusCode).to.eq(200);
            // expect(interception?.response?.body).to.be.an('object');
        });
    }

    private abrirAccordion(tituloAccordion: string): void {
        cy.contains("accordion-header", tituloAccordion)
            .parents(".header-section") // Volta para o elemento pai que contém a classe 'closed'
            .then(($accordion) => {
                // 2. Verifica se está fechado (tem a classe 'closed')
                if ($accordion.hasClass("closed")) {
                    // 3. Se estiver fechado, clica para abrir
                    cy.wrap($accordion).click();
                    // Opcional: Aguarda a animação
                    cy.wait(500);
                }
                // Se já estiver aberto, não faz nada
            });
    }

    private verificarCheckbox(id: string): Cypress.Chainable<boolean> {
        return cy.get(id).then(($input) => {
            return $input.is(":checked"); // Retorna true (marcado) ou false (desmarcado)
        });
    }

    private acaoNegociarPlano(
        aluno: string,
        nomePlano: string = "CREDITO",
        condicaoPagamento: string | number = null
    ): void {
        cy.intercept("GET", "**/negociacao/clientes?**").as("carregarClientes");
        // 1. Busca e seleção do aluno
        cy.get(this.elements.BUSCA_RAPIDA, { timeout: 10000 }).should("be.visible");
        cy.get(this.elements.BUSCA_RAPIDA).type(aluno);
        cy.wait("@carregarClientes");

        cy.get(this.elements.ALUNO, { timeout: 10000 })
            .should("be.visible")
            .click();

        this.interceptarRequisicoes();
        this.aguardarSimularContrato();

        // 2. Lógica do plano
        cy.get(this.elements.SELECT_PLANO, { timeout: 15000 })
            .should("be.visible")
            .find("option:selected")
            .invoke("text")
            .then((planoAnterior) => {
                const mudouPlano =
                    nomePlano.trim().toLowerCase() !== planoAnterior.trim().toLowerCase();
                if (mudouPlano) {
                    cy.get(this.elements.SELECT_PLANO).select(nomePlano);
                    this.interceptarRequisicoes();
                    this.aguardarSimularContrato();
                }
            })
            .then(() => {
                // 3. Lógica da condição de pagamento (ATUALIZADA)
                if (condicaoPagamento != null) {
                    cy.get(this.elements.SELECT_CONDICAO, { timeout: 15000 })
                        .should("be.visible")
                        .should("not.be.empty")
                        .find("option:selected")
                        .then(($opcaoSelecionada) => {
                            const textoOpcao = $opcaoSelecionada.text().trim().toLowerCase();
                            const valorOpcao = $opcaoSelecionada
                                .val()
                                ?.toString()
                                .trim()
                                .toLowerCase();
                            const indexOpcao = $opcaoSelecionada.index(); // Índice da opção (0, 1, 2...)
                            const condicaoDesejada = String(condicaoPagamento)
                                .trim()
                                .toLowerCase();

                            // Verifica se:
                            // 1. O valor passado é igual ao "value" da opção OU
                            // 2. É igual ao índice da opção (ex.: 1) OU
                            // 3. Está contido no texto da opção (ex.: "vista" em "A Vista")
                            const condicaoJaEstaSelecionada =
                                valorOpcao === condicaoDesejada ||
                                String(indexOpcao) === condicaoDesejada ||
                                textoOpcao.includes(condicaoDesejada);

                            if (!condicaoJaEstaSelecionada) {
                                cy.get(this.elements.SELECT_CONDICAO).select(condicaoPagamento);
                                this.interceptarRequisicoes();
                                this.aguardarSimularContrato();
                            } else {
                                console.log("Condição já selecionada:", {
                                    valorPassado: condicaoDesejada,
                                    valorOpcao,
                                    indexOpcao,
                                    textoOpcao,
                                });
                            }
                        });
                }
            });
    }

    finalizarComLinkPagamento() {
        cy.get(this.elements.ENVIAR_LINK_PAGAMENTO).scrollIntoView();
        cy.get(this.elements.ENVIAR_LINK_PAGAMENTO).should("be.visible");
        cy.get(this.elements.ENVIAR_LINK_PAGAMENTO).click();
        cy.get(this.elements.SENHA).type("123");
        cy.get(this.elements.CONFIRMAR_SENHA).click();
        this.aguardarLoader();
    }

    interceptarRequisicoes() {
        cy.intercept("POST", "**/negociacao/gravar").as("gravarContrato");
        cy.intercept("POST", "**/negociacao/simular").as("simularContrato");
    }

    validaParcelasNegociacao(
        quantidade: number,
        primeiraParcela: string,
        parcelas: string
    ) {
        cy.get(this.elements.TABELA_PARCELAS_NEGOCIACAO)
            .should("have.length", quantidade)
            .each(($el, index) => {
                const expectedValue = index === 0 ? primeiraParcela : parcelas;
                cy.log(JSON.stringify(index));
                cy.wrap($el).contains(expectedValue);
            });
    }

    negociarPlanoCredito(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "CREDITO");

        this.aguardarLoader();
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarPlanoEDeixarCaixaEmAberto(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "FAZ TUDO");
        this.finalizarComLinkPagamento();
        cy.visit(EnvUtils.admFrontUrl() + "/pt/adm");
    }

    negociarPlanoComValorDeHorarioVariavel(
        nomeAluno: string,
        nomePlano: string,
        horario: string,
        valorMensal: number
    ): void {
        const valorMensalidade = valorMensal.toLocaleString("pt-BR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
        const valorFinal = (valorMensal * 12).toFixed(2);
        const valorFinalFormatado = parseFloat(valorFinal).toLocaleString("pt-BR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
        cy.visit(EnvUtils.admFrontUrl() + "/pt/adm/negociacao/contrato");
        this.novaAcaoLancarContrato(nomeAluno, nomePlano);
        this.aguardarLoader();
        cy.get(this.elements.SELECT_HORARIO).select(horario, { timeout: 40000 });
        cy.get(this.elements.VALOR_TOTAL_NEGOCIACAO).contains(valorFinalFormatado);
        this.finalizarComLinkPagamento();
    }

    negociarPlanoComPacoteVariavel(
        nomeAluno: string,
        nomePlano: string,
        valorMensal: number
    ): void {
        this.acaoNegociarPlano(nomeAluno, nomePlano);

        cy.wait(1500);
        cy.get(this.elements.SELECT_MODALIDADE1_PACOTE).select("FAZ TUDO");
        this.aguardarSimularContrato();

        cy.wait(1500);
        cy.get(this.elements.SELECT_MODALIDADE2_PACOTE).select("PLANO NORMAL");
        this.aguardarSimularContrato();

        cy.get(this.elements.VALOR_MODALIDADE1_PACOTE).should(
            "contain",
            "R$ 50,00"
        );
        cy.get(this.elements.VALOR_MODALIDADE2_PACOTE).should(
            "contain",
            "R$ 50,00"
        );
        cy.get(this.elements.VALOR_TOTAL_PLANO).contains("R$ 1.200,00");

        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarPlanoComPacoteFixo(
        nomeAluno: string,
        nomePlano: string,
        valorMensal: number
    ): void {
        const valorFinal = (valorMensal * 12).toFixed(2);
        const valorFinalFormatado = parseFloat(valorFinal).toLocaleString("pt-BR", {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        });
        this.acaoNegociarPlano(nomeAluno, nomePlano);
        cy.contains("MUAY THAI").should("exist");
        cy.get(this.elements.VALOR_TOTAL_NEGOCIACAO).contains(valorFinalFormatado);

        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarPlanoComParcelaDiferente(
        nomeAluno: string,
        nomePlano: string,
        quantidadeParcelas: number,
        valorPrimeiraParcela: string,
        valorMensal: string,
        valorFinal: string
    ): void {
        MenuSuperiorPage.menuExplorar("Negociação", ModulosEnum.ADM_LEGADO);
        this.acaoNegociarPlano(nomeAluno, nomePlano, "EM 12 VEZES");
        cy.get(this.elements.VALOR_TOTAL_NEGOCIACAO).contains(valorFinal);
        this.finalizarComLinkPagamento();
    }

    renovarplano(aluno: string): void {
        cy.intercept("GET", "**/negociacao/clientes?**").as("carregarClientes");
        // 1. Busca e seleção do aluno
        cy.get(this.elements.BUSCA_RAPIDA, { timeout: 10000 }).should("be.visible");
        cy.get(this.elements.BUSCA_RAPIDA).type(aluno);
        cy.wait("@carregarClientes");

        cy.get(this.elements.ALUNO, { timeout: 10000 })
            .should("be.visible")
            .click();

        this.aguardarSimularContrato();

        cy.get(this.elements.RENOVACAO_CONTRATO_SIM, { timeout: 15000 })
            .should("be.visible")
            .click();

        cy.wait(5000);

        cy.get("body").then(($body) => {
            if ($body.text().includes("Aluno possui autorização de")) {
                // Texto encontrado
                // NÃO FAZER NADA
            } else {
                // Texto NÃO encontrado, seguir fluxo
                cy.get(this.elements.AUTORIZACAO_COBRANCA, { timeout: 15000 })
                    .should("be.visible")
                    .click();
                cy.get(this.elements.CREDITO).should("be.visible").click();
                cy.get(this.elements.SELECIONAR_CONVENIO)
                    .should("be.visible")
                    .select("STONE ONLINE");
                cy.get(this.elements.NUMERO_CARTAO)
                    .should("be.visible")
                    .clear()
                    .type("****************");
                cy.get(this.elements.TITULAR_CARTAO)
                    .should("be.visible")
                    .clear()
                    .type("TESTE CARTAO");
                cy.get(this.elements.VALIDADE_CARTAO)
                    .should("be.visible")
                    .clear()
                    .type("1250");
                cy.get(this.elements.CVV_CARTAO)
                    .should("be.visible")
                    .clear()
                    .type("123");
                cy.get(this.elements.BOTAO_SALVAR_AUTORIZACAO)
                    .should("be.visible")
                    .click();
                cy.get(this.elements.MENSAGEM_MODAL)
                    .should("be.visible")
                    .should("contain", "Salvo com sucesso!");
                cy.contains("Ok").should("be.visible").click();
            }
        });

        cy.wait(500);
        cy.get(this.elements.ENVIAR_LINK_PAGAMENTO).click();
        this.aguardarGravarContrato();

        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarPlano(aluno: string, plano: string) {
        this.novaAcaoLancarContrato(aluno, plano.toUpperCase());

        this.finalizarComLinkPagamento();

        cy.get(this.elements.FECHAR_ABA, { timeout: 10000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 10000 }).click();
    }

    negociarPlanoEAlterarAData(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "FAZ TUDO");
        this.aguardarLoader();

        cy.contains(".pacto-button", "Editar", { timeout: 15000 }).first().click();
        cy.get(".modal-titulo", { timeout: 20000 }).contains(
            "Configurações de contrato"
        );

        cy.get(this.elements.DATA_NEGOCIACAO).clear();
        cy.get(this.elements.DATA_NEGOCIACAO)
            .type(DateUtils.futureDateBR(-30))
            .wait(1000);
        cy.get(this.elements.DATA_NEGOCIACAO).blur();
        cy.wait(1000);

        cy.get('[data-cy="btn-salvar"]').click();

        this.aguardarLoader();

        this.finalizarComLinkPagamento();

        cy.get(this.elements.FECHAR_ABA, { timeout: 50000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 50000 }).click();

        cy.wait(500);
    }

    negociarDescontoManualPorConvenio(aluno: string): void {
        this.acaoNegociarPlano(aluno, "| SLIM | ARTES MARCIAIS", 0);

        cy.get(".header-section", { timeout: 15000 })
            .eq(1) // Pega o segundo elemento (índice 1)
            .should("be.visible")
            .then(($element) => {
                if ($element.hasClass("closed")) {
                    cy.wrap($element).click({ force: true }); // Clica para abrir
                    cy.wait(1000); // Aguarda um pouco para garantir que abriu
                }
                cy.wrap($element).should("not.have.class", "closed"); // Garante que abriu
            });

        cy.get(this.elements.DESCONTO_TIPO).select("Convênio");
        cy.get(this.elements.CONVENIO_DESCONTO)
            .select("3")
            .should("have.value", "3");
        cy.get(this.elements.ADICIONAR_DESCONTO_PRODUTO).click();

        cy.get(this.elements.VALOR_FINAL_DESCONTO).should("contain", "20,00");

        cy.get(this.elements.SELECT_CONDICAO, { timeout: 15000 })
            .should("be.visible")
            .select(0);
        cy.get(this.elements.ENVIAR_LINK_PAGAMENTO)
            .should("not.be.disabled")
            .click();
        cy.get(this.elements.SENHA).type("123");
        cy.get(this.elements.CONFIRMAR_SENHA)
            .should("not.be.disabled")
            .and("be.visible")
            .click({ force: true });
        cy.get(this.elements.NOTIFICACAO).contains("Contrato lançado com sucesso!");
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    validarContratoDescontoConvenio(aluno: string): void {
        cy.wait(5000);
        cy.get("#module-ntr").click();
        cy.get("#topbar-search-field").type(aluno);
        cy.contains(aluno).click();

        cy.intercept(
            "POST",
            "**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste"
        ).as("ultimaRequisicao");
        cy.wait("@ultimaRequisicao", { timeout: 50000 });
        cy.contains("Ativo", { timeout: 50000 }).should("be.visible");
        cy.get(this.elements.ABA_FINANCEIRO).click();
        cy.get(this.elements.DESCONTO_PERFIL_DO_ALUNO).should(
            "contain",
            "Matrícula"
        );
        cy.get(this.elements.DESCONTO_VALOR_CONVENIO).should("contain", "20,00");
    }

    negociarPlanoFixandoAlunoEmColetivas(aluno: ClientePessoa) {
        this.novaAcaoLancarContrato(aluno.pessoa.nome, "FAZ TUDO");
        this.aguardarLoader();

        cy.get("span").contains("Editar horários").click();
        cy.get(this.elements.FILTRO_PROFESSOR)
            .select("PACTO - MÉTODO DE GESTÃO")
            .wait(1000);
        for (let i = 2; i <= 7; i++) {
            cy.get(`.linha-agenda > :nth-child(${i}) > :nth-child(1)`)
                .first()
                .click({ force: true });
        }
        cy.get(this.elements.AVISO_HORARIOS).contains(
            "Você escolheu todos os horários permitidos"
        );
        cy.contains("span", "Confirmar").click();

        this.aguardarLoader();

        this.finalizarComLinkPagamento();

        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarPlanoCreditoComDescontoProduto(aluno: string): void {
        this.acaoNegociarPlano(aluno, "2. MENSAL FIT", 0);

        this.abrirAccordion("Produtos");

        cy.get(this.elements.EDITAR_DESCONTO).click();
        cy.get(this.elements.INPUT_DESCONTO_PRODUTO).clear().type("10,00");
        cy.get(this.elements.EDITAR_DESCONTO).click();

        cy.get(this.elements.ENVIAR_LINK_PAGAMENTO)
            .should("not.be.disabled")
            .click();
        cy.get(this.elements.SENHA).type("123");
        cy.get(this.elements.CONFIRMAR_SENHA)
            .should("not.be.disabled")
            .and("be.visible")
            .click({ force: true });
        this.aguardarGravarContrato();
        cy.contains("Contrato lançado com sucesso!").should("be.visible");
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();

        cy.wait(500);
    }

    negociarPlanoECobrarProdutoSeparadamente(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "TESTE AUTOMATIZADO");
        this.aguardarLoader();

        cy.contains("Mais").click();
        cy.contains("Opções avançadas").click();

        // Verificação correta do estado do checkbox
        this.verificarCheckbox("#check-matricula-separada-input").then(
            (checkboxMarcado) => {
                console.log("Checkbox marcado:", checkboxMarcado);
                if (checkboxMarcado) {
                    // Se estiver marcado, desmarca
                    cy.get("#check-matricula-separada").should("be.visible");
                    cy.get("#check-matricula-separada").get("input").click();
                }
                cy.get(this.elements.SALVAR_CONFIG_AVANCADA).click();

                cy.contains("1 ª Parcela - R$ 99,99").should("be.visible");

                cy.contains("Mais").click();
                cy.contains("Opções avançadas").click();
                cy.get(this.elements.COBRAR_MATRICULA_SEPARADAMENTE).click();
                cy.get(this.elements.SALVAR_CONFIG_AVANCADA).click();

                cy.contains("Matrícula - R$ 10,00").should("be.visible");
            }
        );

        this.finalizarComLinkPagamento();

        cy.get(this.elements.FECHAR_ABA, { timeout: 10000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 10000 }).click();
    }

    validarPlanoECobrarProdutoSeparadamente(aluno: string): void {
        cy.get("#module-ntr", { timeout: 15000 }).click();
        cy.get("#topbar-search-field").type(aluno);
        cy.contains(aluno).click();
        cy.intercept(
            "POST",
            "**/ZillyonWeb/prest/tela-cliente?op=contrato&key=teste"
        ).as("ultimaRequisicao");
        cy.wait("@ultimaRequisicao", { timeout: 50000 });

        cy.contains("Ativo", { timeout: 50000 }).should("be.visible");
        cy.get(this.elements.ABA_FINANCEIRO).click();

        cy.contains("Histórico de Parcelas").trigger("mouseover");
        cy.contains("Matrícula Parcela 1").should("be.visible");

        cy.wait(500);
    }

    negociarPlanoEDividirProdutosNasParcelas(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "FAZ TUDO");
        this.aguardarLoader();

        cy.contains("Mais").click();
        cy.contains("Opções avançadas").click();
        cy.get(this.elements.DIVIDIR_PRODUTO_PARCELAS).click();
        cy.get(this.elements.SALVAR_CONFIG_AVANCADA).click();
        this.aguardarLoader();

        cy.contains("1 ª Parcela - R$ 80,90").should("be.visible");

        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarDescontoManualPorcentual(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "FAZ TUDO");

        this.abrirAccordion("Desconto e convênio");
        cy.get(this.elements.DESCONTO_TIPO).select("Extra por percentual");
        cy.get(this.elements.VALOR_DESCONTO_PERCENTUAL).type("5");
        cy.get(this.elements.VALOR_DESCONTO_PERCENTUAL).should("have.value", "5");
        cy.get(this.elements.ADICIONAR_DESCONTO_PRODUTO).click();
        this.aguardarLoader();

        cy.get(this.elements.VALOR_FINAL_DESCONTO).should("contain", "48,54");

        this.finalizarComLinkPagamento();

        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarDescontoManualExtra(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "FAZ TUDO");

        this.abrirAccordion("Desconto e convênio");
        cy.get(this.elements.DESCONTO_TIPO).select("Extra por valor");
        cy.get(this.elements.VALOR_DESCONTO).type("50,00");
        cy.get(this.elements.VALOR_DESCONTO).should("have.value", "50,00");
        cy.get(this.elements.ADICIONAR_DESCONTO_PRODUTO).click();
        this.aguardarLoader();

        cy.get(this.elements.VALOR_FINAL_DESCONTO).should("contain", "50,00");

        this.finalizarComLinkPagamento();
        cy.visit(EnvUtils.admFrontUrl() + "/pt/adm");
    }

    negociarPlanoComProRata(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "TESTE AUTOMATIZADO RECORRÊNCIA");
        this.aguardarLoader();

        cy.contains("div", "Vencimento das parcelas no dia")
            .should("be.visible")
            .siblings("div")
            .find("select")
            .should("exist")
            .select("29")
            .trigger("change");

        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
    }

    negociarPlanoCupomDesconto(aluno: string): void {
        this.novaAcaoLancarContrato(aluno, "TESTE AUTOMATIZADO RECORRÊNCIA");

        this.abrirAccordion("Desconto e convênio");
        cy.get(this.elements.TIPO_DESCONTO, { timeout: 15000 }).select("Cupom");
        cy.get(this.elements.DESCONTO_CONVENIO, { timeout: 15000 })
            .should("exist")
            .should("be.visible")
            .type("100DESC");
        cy.get(this.elements.VALIDAR_VALOR)
            .invoke("text")
            .then(function (text) {
                var textoNormalizado = text.replace(/\s/g, "").replace("R$", ""); // Remove espaços e "R$"
                expect(textoNormalizado).to.eq("1.440,00");
            });

        //selecionar o cupom
        cy.get(this.elements.ADICIONAR_DESCONTO_PRODUTO).click();
        this.aguardarLoader();
        cy.wait(2500);

        cy.get(this.elements.VALIDAR_VALOR)
            .invoke("text")
            .then(function (text) {
                var textoNormalizado = text.replace(/\s/g, ""); // Remove espaços

                // Se o valor não começar com "R$", adiciona manualmente
                if (!textoNormalizado.startsWith("R$")) {
                    textoNormalizado = "R$" + textoNormalizado;
                }

                expect(textoNormalizado).to.eq("R$1.320,00");
            });

        //////Validar valor da parcela com desconto/////
        cy.get(this.elements.PARCELA_NEGOCIACAO)
            .invoke("text")
            .then((text) => {
                var textoNormalizado = text.replace(/\s+/g, " ").trim(); // Normaliza espaços extras
                expect(textoNormalizado).to.contain("1 ª Parcela - R$ 0,00 100DESC");
            });

        this.finalizarComLinkPagamento();

        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    excluirModalidade(aluno: string, modalidade: string): void {
        this.novaAcaoLancarContrato(aluno, "TESTE AUTOMATIZADO");
        this.aguardarLoader();
        cy.get(this.elements.EXCLUIR_MODALIDADE).should("be.visible").click();
        this.aguardarLoader();
        cy.get(this.elements.TABELA_MODALIDADE).should("not.contain", modalidade); //Validar se modalidade foi excluida
    }

    adicionarModalidade(modalidade: string): void {
        cy.get(this.elements.TABELA_MODALIDADE).should("be.visible").click();
        cy.get(this.elements.ADICIONAR_ITEM_PACOTE).should("be.visible").click();
        cy.get(this.elements.SELECT_MODALIDADE).should("be.visible").click();
        cy.get(this.elements.LUPA_MODALIDADE)
            .should("be.visible")
            .type(modalidade) //Validar Lupa de pesquisa da modalidade
            .wait(5000);
        cy.get(this.elements.TEXTO_MODALIDADE).should("be.visible");
        cy.get(this.elements.OPCAO_INPUT).contains(modalidade).click();
        this.aguardarLoader();
        cy.get(this.elements.TABELA_MODALIDADE)
            .should("be.visible")
            .should("contain", modalidade); //Validar se modalidade carregou com sucesso
    }

    negociarPlanoRecorrente(aluno: string, plano: string, valorFinal: string) {
        this.novaAcaoLancarContrato(aluno, plano);
        cy.get(this.elements.VALOR_TOTAL_NEGOCIACAO).contains(valorFinal);
        this.finalizarComLinkPagamento();
        cy.get(this.elements.NOTIFICACAO, { timeout: 10000 }).contains(
            "Contrato lançado com sucesso!"
        );
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    negociarPlanoComTurma(nomeAluno: string, nomePlano: string): void {
        //this.acaoNegociarPlano(nomeAluno, nomePlano, null)
        this.novaAcaoLancarContrato(nomeAluno, nomePlano);
        cy.get(this.elements.EDITAR_HORARIOS)
            .should("be.visible")
            .click({ force: true });
        this.aguardarLoader();
        cy.get(this.elements.HORARIO_QUARTA).click().wait(1000);
        cy.get(this.elements.HORARIO_SEXTA).click().wait(1000);
        cy.get(this.elements.AVISO_HORARIOS)
            .contains("Você escolheu todos os horários permitidos")
            .wait(500);
        cy.get(this.elements.CONFIRMAR_HORARIOS).click().wait(500);
        this.aguardarLoader();
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.contains("Contrato lançado com sucesso!", { timeout: 60000 }).should(
            "be.visible"
        );
    }

    lancarPlanoEArredondarParcela(
        aluno: string,
        nomePlano: string = "FAZ TUDO"
    ): void {
        this.novaAcaoLancarContrato(aluno, nomePlano);
        this.aguardarLoader();

        cy.get(this.elements.VALOR_TOTAL_NEGOCIACAO)
            .invoke("text")
            .then((text) => {
                const textoNormalizado = text.replace(/\s/g, ""); // Remove qualquer espaço invisível
                expect(textoNormalizado).to.eq("R$970,80"); // Sem espaço entre R$ e o número
            });

        //Arredondar o valor
        cy.get(this.elements.BOTAO_MAIS, { timeout: 15000 })
            .should("be.visible")
            .click();
        cy.contains("Arredondar valores", { timeout: 15000 }).click();

        cy.get(".modal-titulo").contains("Valores sugeridos");

        cy.contains("1x R$ 80,00", { timeout: 15000 }).click();

        this.aguardarLoader();

        cy.wait(3000);
        //Validando o valor arredondado
        cy.get(this.elements.VALOR_TOTAL_NEGOCIACAO, { timeout: 15000 }) // Altere para o seletor real onde está "R$960,00".should('be.visible')
            .invoke("text")
            .then((text) => {
                var textoNormalizado = text.trim().replace(/\s/g, "");
                expect(textoNormalizado).to.eq("R$960,00");
            });
        cy.wait(3000);

        this.finalizarComLinkPagamento();
    }

    caixaAberto(): void {
        cy.wait(500);
        cy.get(this.elements.BOTAO_DEIXAR_NO_CAIXA_EM_ABERTO, { timeout: 15000 })
            .should("be.visible")
            .click();
        cy.wait(500);

        //////SENHA/////
        cy.get(this.elements.SENHA).type("123");
        cy.get(this.elements.CONFIRMAR_SENHA).click();
        cy.wait(500);
    }

    autorizacaoCobrancaCartao(aluno): void {
        this.acaoNegociarPlano(aluno);

        cy.get(this.elements.AUTORIZACAO_COBRANCA).should("be.visible").click();
        cy.wait(5000);
        cy.get(this.elements.CREDITO).should("be.visible").click();
        cy.wait(500);
        cy.get(this.elements.SELECIONAR_CONVENIO)
            .should("be.visible")
            .select("DCC CIELO");
        cy.wait(500);
        cy.get(this.elements.NUMERO_CARTAO)
            .should("be.visible")
            .clear()
            .type("****************");
        cy.wait(500);
        cy.get(this.elements.TITULAR_CARTAO)
            .should("be.visible")
            .clear()
            .type("TESTE CARTAO");
        cy.get(this.elements.VALIDADE_CARTAO)
            .should("be.visible")
            .clear()
            .type("1250");
        cy.get(this.elements.CVV_CARTAO).should("be.visible").clear().type("123");
        cy.get(this.elements.BOTAO_SALVAR_AUTORIZACAO).should("be.visible").click();
        cy.wait(500);
        cy.get(this.elements.MENSAGEM_MODAL)
            .should("be.visible")
            .should("contain", "Salvo com sucesso!");
        cy.contains("Ok").should("be.visible").click();
        cy.wait(500);
    }

    autorizacaoCobrancaBoleto(aluno): void {
        this.acaoNegociarPlano(aluno);
        cy.get(this.elements.AUTORIZACAO_COBRANCA).should("be.visible").click();
        cy.get(this.elements.BOLETO).click();
        cy.get(this.elements.SELECIONAR_CONVENIO, { timeout: 15000 }).select(
            "BOLETO"
        );
        cy.get(this.elements.BOTAO_SALVAR).click();
        cy.get(this.elements.MENSAGEM_MODAL, { timeout: 15000 }).should(
            "have.text",
            "Salvo com sucesso!"
        );
        cy.contains("Ok").click();
        cy.wait(500);
    }

    negociarPlanoComProduto(
        aluno: string,
        produto: string,
        quantidade: number,
        valorUnitario: string,
        valorTotal: string
    ): void {
        this.acaoNegociarPlano(aluno, "FAZ TUDO");
        this.abrirAccordion("Produtos");

        cy.get("span").contains("Adicionar produto").click();
        cy.get(this.elements.SELECT_PRODUTO).click();
        cy.get('#select-table-produto-descricao-filter').type('WHEY GOLD')
        cy.get(".options").contains(produto).click();
        cy.get(this.elements.QUANTIDADE_PRODUTO)
            .invoke("val")
            .then((valor) => {
                let quantidadeAtual = parseInt(valor.toString(), 10) || 0;
                if (quantidadeAtual >= quantidade) {
                    return;
                }
                let cliquesNecessarios = quantidade - quantidadeAtual;
                Cypress._.times(cliquesNecessarios, () => {
                    cy.get(this.elements.QUANTIDADE_PRODUTO_AUMENTAR).click();
                });
            });
        cy.get(this.elements.SALVAR_PRODUTO).click();
        this.aguardarSimularContrato();
        cy.get(this.elements.VALOR_PRODUTO).contains(valorUnitario);
        cy.get(this.elements.VALOR_TOTAL_PRODUTO).contains(valorTotal);
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA).click();
        cy.get(this.elements.FECHAR_ABA).click();
    }

    adicionarAutorizacaoCartao(): void {
        cy.get(this.elements.CREDITO).should("be.visible").click();
        cy.wait(500);
        cy.get(this.elements.SELECIONAR_CONVENIO)
            .should("be.visible")
            .select("DCC CIELO");
        cy.wait(500);
        cy.get(this.elements.NUMERO_CARTAO)
            .should("be.visible")
            .clear()
            .type("****************");
        cy.wait(500);
        cy.get(this.elements.TITULAR_CARTAO)
            .should("be.visible")
            .clear()
            .type("TESTE CARTAO");
        cy.get(this.elements.VALIDADE_CARTAO)
            .should("be.visible")
            .clear()
            .type("1250");
        cy.get(this.elements.CVV_CARTAO).should("be.visible").clear().type("123");
        cy.get(this.elements.BOTAO_SALVAR_AUTORIZACAO).should("be.visible").click();
        cy.wait(500);
        cy.get(this.elements.MENSAGEM_MODAL)
            .should("be.visible")
            .should("contain", "Salvo com sucesso!");
        cy.contains("Ok").should("be.visible").click();
        cy.wait(500);
    }

    validarPlanoParaVisitante(nomeAluno: string, nomePlano: string): void {
        cy.intercept("GET", "**/negociacao/clientes?**").as("carregarClientes");

        // 1. Busca e seleção do aluno
        cy.get(this.elements.BUSCA_RAPIDA, { timeout: 10000 }).should("be.visible");
        cy.get(this.elements.BUSCA_RAPIDA).type(nomeAluno);
        cy.wait("@carregarClientes");
        cy.get(this.elements.ALUNO, { timeout: 10000 })
            .should("be.visible")
            .click();

        this.interceptarRequisicoes();
        this.aguardarSimularContrato();

        // 2. Em caso de renovação, avança no processo
        cy.get("body").then(($body) => {
            if ($body.text().includes("Renovação de contrato")) {
                cy.get(this.elements.RENOVAR_CONTRATO).click();
            }
        });

        // 3. Valida se o plano é apresentado no select de Planos
        cy.get(this.elements.SELECT_PLANO, { timeout: 15000 })
            .should("be.visible")
            .and("not.contain", nomePlano);
    }

    negociarPlanoAlterandoData(
        dataLancamento: string,
        nomeAluno: string,
        nomePlano: string
    ): void {
        cy.intercept("GET", "**/negociacao/clientes?**").as("carregarClientes");
        // 1. Busca e seleção do aluno
        cy.get(this.elements.BUSCA_RAPIDA, { timeout: 10000 }).should("be.visible");
        cy.get(this.elements.BUSCA_RAPIDA).type(nomeAluno);
        cy.wait("@carregarClientes");

        cy.get(this.elements.ALUNO, { timeout: 10000 })
            .should("be.visible")
            .click();

        this.interceptarRequisicoes();
        this.aguardarSimularContrato();

        cy.contains("span", "Editar").click();
        cy.get(this.elements.CALENDARIO_DATA_LANCAMENTO).click();
        cy.get(this.elements.CALENDARIO).contains("1").click().wait(2000);
        cy.get(this.elements.BOTAO_SALVAR_DATA).click();
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA, { timeout: 10000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 10000 }).click();

        // 2. Lógica do plano
        cy.get(this.elements.SELECT_PLANO, { timeout: 15000 })
            .should("be.visible")
            .find("option:selected")
            .invoke("text")
            .then((planoAnterior) => {
                const mudouPlano =
                    nomePlano.trim().toLowerCase() !== planoAnterior.trim().toLowerCase();
                if (mudouPlano) {
                    cy.get(this.elements.SELECT_PLANO).select(nomePlano);
                    this.aguardarSimularContrato();
                }
            });
    }

    validaCarregamentoTela(): void {
        cy.get(this.elements.HEADER_NEGOCIACAO).contains("Negociação");
        cy.get(this.elements.SELECT_PLANO, { timeout: 10000 }).should("be.visible");
    }

    validarProdutoAdesaoNaRenovacao(nomeAluno: string, nomePlano: string): void {
        this.selecionarAluno(nomeAluno);
        this.aguardarLoader();

        cy.get(this.elements.CONFIRMAR_RENOVACAO).click();
        this.aguardarLoader();

        this.selecionarPlano(nomePlano);

        this.abrirAccordion("Produtos");
        cy.get(this.elements.LISTA_PRODUTOS_NEGOCIACAO).should(
            "not.have.text",
            "ADESÃO PLANO RECORRENTE"
        );
    }

    private selecionarAluno(nomeCliente: string): void {
        cy.intercept("GET", "**/negociacao/clientes?**").as("carregarClientes");
        cy.get(this.elements.BUSCA_RAPIDA, { timeout: 10000 }).should("be.visible");
        cy.get(this.elements.BUSCA_RAPIDA).type(nomeCliente);
        cy.wait("@carregarClientes");

        cy.get(this.elements.ALUNO, { timeout: 10000 })
            .should("be.visible")
            .click();
    }

    private aguardarLoader(timeout: number = 10000): void {
        cy.get(this.elements.LOADER, { timeout: timeout }).should("not.exist");
        cy.wait(1500);
    }

    private selecionarPlano(nomePlano: string): void {
        this.aguardarLoader();
        cy.get(this.elements.SELECT_PLANO, { timeout: 15000 })
            .should("be.visible")
            .select(nomePlano.toUpperCase());
        this.aguardarLoader();
        cy.get(this.elements.SELECT_PLANO)
            .find("option:selected")
            .invoke("text")
            .then((planoSelecionado) => {
                expect(planoSelecionado.trim().toUpperCase()).to.eq(
                    nomePlano.trim().toUpperCase()
                );
            });
    }

    novaAcaoLancarContrato(nomeCliente: string, nomePlano: string): void {
        this.selecionarAluno(nomeCliente);
        this.aguardarLoader();
        this.selecionarPlano(nomePlano);
    }

    novoLancarContrato(
        nomeCliente: string,
        nomePlano: string,
        senha: boolean = false
    ): void {
        this.selecionarAluno(nomeCliente);
        this.aguardarLoader();
        this.selecionarPlano(nomePlano);
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.contains("Contrato lançado com sucesso!", { timeout: 60000 }).should(
            "be.visible"
        );
    }

    novoLancarContratoReceber(
        nomeCliente: string,
        nomePlano: string,
        senha: boolean = false
    ): void {
        this.selecionarAluno(nomeCliente);
        this.aguardarLoader();
        this.selecionarPlano(nomePlano);
        cy.wait(3000)
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).scrollIntoView();
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).should("be.visible");
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).click()
    }

    novoRenovarContrato(nomeCliente: string): void {
        this.selecionarAluno(nomeCliente);
        this.aguardarLoader();
        cy.get(this.elements.RENOVACAO_CONTRATO_SIM, { timeout: 15000 })
            .should("be.visible")
            .click();
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.contains("Contrato lançado com sucesso!", { timeout: 60000 }).should(
            "be.visible"
        );
    }

    private alterarDataLancamento(data: string): void {
        this.aguardarLoader();
        cy.get(".info-top").contains("Editar", { timeout: 60000 }).click();
        cy.get(this.elements.INPUT_DATA_LANCAMENTO)
            .clear()
            .type(data)
            .blur()
            .wait(1500);
        cy.get(".modal-titulo").click().wait(2000);
        cy.get(this.elements.BOTAO_SALVAR_DATA, { timeout: 60000 }).click();
        this.aguardarLoader();
    }

    lancarPlanoAlterandoData(
        nomeCliente: string,
        nomePlano: string,
        data: string
    ): void {
        this.novaAcaoLancarContrato(nomeCliente, nomePlano);
        this.aguardarLoader();
        this.alterarDataLancamento(data);
        this.finalizarComLinkPagamento();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.get(this.elements.FECHAR_ABA, { timeout: 60000 }).click();
        cy.contains("Contrato lançado com sucesso!", { timeout: 60000 }).should(
            "be.visible"
        );
    }
}

export default new NegociacaoPage();
