class CidadePage {

    elements = {
        ADICIONAR: '#btn-add-item',
        NOME: '[title="Nome"]',
        PAIS: '.current-value',
        ESTADO:'.current-wrapper .option-label',
        FILTRO:'[placeholder="Filtro..."]',
        BUSCA: '#input-busca-rapida',
        EXCLUIR:'#element-0-action-delete\\ \\(key\\)'


    }

    adicionarCidade() {
        cy.get(this.elements.ADICIONAR).click()
        cy.get(this.elements.NOME).click().type('APARECIDA DE GOIANIA')
        cy.get(this.elements.PAIS).eq(0).click();
        cy.get('.options').should('be.visible');
        cy.contains('.option-label', 'BRASIL').click();
        cy.get(this.elements.ESTADO).eq(1).click();
        cy.get(this.elements.FILTRO).eq(1).type('GOIÁS');
        cy.contains('.option-label', 'GOIÁS').click();
        cy.contains('Salvar').click()
        cy.contains('Cidade cadastrada com sucesso!').should('be.visible')

    }

    editarCidade() {
        cy.intercept('POST', '**/cidades').as('editarCidade');
    
        cy.get(this.elements.BUSCA, { timeout: 15000 })
            .should('be.visible')
            .click()
            .type('APARECIDA DE GOIANIA');
    
        cy.contains('APARECIDA DE GOIANIA').click();
        cy.get(this.elements.NOME, { timeout: 15000 }).clear().type('APARECIDA DE GOIANIA 02');
        cy.contains('Salvar').click();
    
        
        cy.wait('@editarCidade').its('request.body').should((body) => {
            expect(body.nome).to.eq('APARECIDA DE GOIANIA 02');
        });
    
        cy.contains('Cidade cadastrada com sucesso!').should('be.visible');
    }
    excluirCidade() {
    
        cy.intercept('GET', '**/cidades?*').as('buscarCidade');

        cy.get(this.elements.BUSCA, { timeout: 15000 })
          .should('be.visible')
          .click()
          .type('APARECIDA DE GOIANIA 02');
        
        cy.wait('@buscarCidade');
        
        
        cy.contains('APARECIDA DE GOIANIA 02', { timeout: 10000 }).should('be.visible');
        cy.get(this.elements.EXCLUIR, { timeout: 15000 }).click(); 
    
        cy.contains('Cidade excluída com sucesso.').should('be.visible');
    }
}

export default new CidadePage();