import MenuSuperiorPage from "./MenuSuperiorPage";
import LoginPage from "@pages/LOGIN/LoginPage";
import EnvUtils from "@utils/EnvUtils";
import { NOVO_ADMINISTRATIVO } from "@models/ModulosEnum";
import { FANTASIA } from "@models/EmpresasEnum";

class RelatorioPage {
    elements = {
        tituloTicketMedio: '#form\\:containerTicketMedio > .bi-header > .bi-panel > .bi-titulo',
        tituloControleOperacoes: '#form\\:containerControleOp .bi-titulo.pull-left',
        botaoAbrirControleOperacoes: '#form\\:consultarContOp > .tooltipster',
        botaoRenegociacoesParcelas: '#form\\:j_id_jsp_563561737_119pc35',
        tabelaResultados: '#form\\:tabelaRes',
        headerRelatorio: 'h2',

        filtroTicketMedioButton: '#filtroTicketMedio',
        checkboxInadimplentes: '#formPanelFiltroTM\\:tabelaFiltroTicket\\:4\\:filtroTMCheckBox',
        atualizarTMButton: '#formPanelFiltroTM\\:atualizarTM',
        consultarTicketButton: '#form\\:consultarTicket > .fa-icon-refresh',
        containerTicketMedio: '#form\\:containerTicketMedio',
        tituloDesconsiderar: ':nth-child(5) > :nth-child(1) > .tituloCampos',

        matriculaInput: '#form\\:matricula',
        registrarAcessosButton: '#form\\:registrarAcessos',
        registrarAcessosModalButton: '#formAce\\:btnRegistrarModal',
        modalMensagem: '#messageInfo',
        messageInfo: '#messageInfo',
        biHeader: '.bi-gestaoAcesso > .bi-header > .bi-panel > .bi-titulo',
        acessosHojeLink: () => cy.contains('Acessos realizados hoje').parent().parent().find('a'),

        adicionarCliente: '#form\\:linkaddClienteMenuSuperiorZWUI > .pct',
        termosConsulta: '#form\\:termosConsulta',
        botaoConsultarCliente: '#form\\:btnConsultarCliente',
        botaoNovo: '#form\\:btnNovo',
        botaoSalvar: '[id="form:salvar"]',
        consultor: '#form\\:consultor',
        cadastrarVisitante: '#form\\:cadastrarVisitante2',
        clienteNomeAuto: '#form\\:clienteNomeAuto',
        botaoGravar: '#formGravarBV\\:btnSimCadastrarVisitante',
        valorConsulta: '#form\\:valorConsulta',
        situacaoSelect: '[id="form:situacao"]',
        botaoConsultar: '#form\\:consultar',
        nomeCliente: '#form\\:tabelaCliente\\:0\\:nomeCliente',
        btnNovoContrato: '#form\\:btnNovoContratoLista',
        planoSelect: '#form\\:plano',
        marcarDuracao: '#form\\:planoDuracaoVO\\:0\\:btnMarcarDuracao',
        diasVencimentoCartao: '#form\\:diasVencimentoCartao',
        conferirNegociacao: '#form\\:btnConferirNegociacao',
        valorFinalContrato: '#form\\:valorFinalContrato',
        totalMensalidadeProRata: '#form\\:movProduto\\:0\\:totalMensalidade',
        totalMensalidadeMensal: '#form\\:movProduto\\:5\\:totalMensalidade',
        botaoPagar: '#form\\:botaoPagar',
        senhaAutorizacao: '#formSenhaAutorizacao\\:senha',
        botaoAutorizar: '#formSenhaAutorizacao\\:btnAutorizar',
        botaoConfirmar: '#form\\:botaoConfirmarcao',
        biHeaderConversao: '[id="form:bi-header-conversao"]',
        icvContainerNumeros: '#form\\:icv-container-numeros > :nth-child(4)',
        fundoBranco: '.fundoBranco',

    }

    pesquisaNaLupa(text) {
        cy.get('.zw_pesquisa_global').type(text)
        cy.wait(2000)
        cy.get('.richfaces_suggestionEntry > :nth-child(2)').click()
        cy.capturaPopUp()

    }

    loginInicio() {
        cy.visit('faces/relatorio/clientePorAniversarioRel.jsp')
        cy.wait(2500)
        cy.get('#formDescobridor\\:userKey').type('teste')
        cy.get('#formDescobridor\\:userName').type('enzo')
        cy.get('#formDescobridor\\:userPassword').type('123')
        cy.get('#formDescobridor\\:descobrir').click()
        cy.wait(3000)
        cy.get('#formEmpresas\\:unidade').click({ force: true })
        cy.get('#formModulo\\:linkAbrirZillyon').click()
        cy.wait(4000)
    }

    loginInicioPactoBr() {
        cy.visit('faces/relatorio/clientePorAniversarioRel.jsp')
        cy.wait(2500)
        cy.get('#formDescobridor\\:userKey').type('teste')
        cy.get('#formDescobridor\\:userName').type('pactobr')
        cy.get('#formDescobridor\\:userPassword').type('123')
        cy.get('#formDescobridor\\:descobrir').click()
        cy.wait(3000)
        cy.get('#formModulo\\:linkAbrirZillyon').click()
        cy.wait(4000)
    }

    clienteAniversario() {
        this.loginInicio()
        cy.visit('faces/relatorio/clientePorAniversarioRel.jsp')
        cy.get('#form\\:empresa', { timeout: 10000 }).should('be.visible')
        cy.get('#form\\:empresa').select('1')
        cy.get('#form\\:pessoa').should('be.visible')
        cy.get('#form\\:pessoa').select('CL')
        cy.get('#form\\:clienteAtivo').should('be.visible')
        cy.wait(2000)
        cy.get('#form\\:clienteAtivo').click()
        cy.get('#form\\:imprimirPDF').should('be.visible')
        cy.get('#form\\:imprimirPDF').click()

    }

    gerarRelatorioAniversarioErrado() {
        this.loginInicio()
        cy.visit('faces/relatorio/clientePorAniversarioRel.jsp')
        cy.get('#form\\:dataInicioInputDate').should('be.visible')
        cy.get('#form\\:dataInicioInputDate').clear()
        cy.get('#form\\:dataTerminoInputDate').clear()
        cy.get('#form\\:imprimirPDF').click()
        cy.get('.mensagemDetalhada').should('contain', 'Não é possível emitir o relatório. Informe primeiro a Data De Início da Pesquisa.')


    }

    validarFluxoPorLupa() {
        LoginPage.logar(EnvUtils.usuarioGustavoProfessor(), NOVO_ADMINISTRATIVO, FANTASIA)
        cy.capturaPopUp()
        MenuSuperiorPage.buscarFuncionalidadesNaLupa('Aniversariantes', { matchMode: 'exact' })
        cy.get('#form\\:imprimirPDF').should('exist')
        cy.contains('Relatório de Aniversariantes')
    }

    AniversarioColaborador() {
        this.loginInicio()
        cy.visit('faces/relatorio/clientePorAniversarioRel.jsp')
        cy.get('#form\\:dataInicioInputDate').should('be.visible')
        cy.get('#form\\:dataInicioInputDate').should('be.visible').type('15/03/2017')
        cy.get('#form\\:dataTerminoInputDate').should('be.visible')
        cy.get('#form\\:dataTerminoInputDate').should('be.visible').type('15/03/2025')
        cy.get('#form\\:empresa').should('be.visible')
        cy.get('#form\\:empresa').select('1')
        cy.get('#form\\:pessoa').select('CO')
        cy.get('#form\\:clienteAtivo').should('be.visible')
        cy.get('#form\\:clienteAtivo').click({ force: true })
        cy.get('#form\\:imprimirPDF').should('be.visible')
        cy.get('#form\\:imprimirPDF').click()
        cy.contains('Total')
    }

    filtroDadosPessoais() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:nome').should('be.visible')
        cy.get('#form\\:nome').clear().type('AA_PRIMEIRO CLIENTE')
        cy.get('#form\\:rg').type('03312654033126540331');
        cy.get('#form\\:inicioCadastroInputDate').should('be.visible')
        cy.get('#form\\:inicioCadastroInputDate').clear().type('28/03/2019')
        cy.get('#form\\:fimCadastroInputDate')
        cy.get('#form\\:fimCadastroInputDate').clear().type('28/03/2019')
        cy.get('#form\\:inicioIdade').should('be.visible')
        cy.get('#form\\:inicioIdade').clear().type('25')
        cy.get('#form\\:fimIdade').should('be.visible')
        cy.get('#form\\:fimIdade').clear().type('25')
        cy.get('#form\\:sexo').should('be.visible')
        cy.get('#form\\:sexo').select('M')
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
    }

    filtroAniversarianteMes() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:anivMes').should('be.visible')
        cy.get('#form\\:anivMes').select('Setembro')
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')

    }

    filtroDataVencimento() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').should('be.visible')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').click()
        cy.get('#form\\:inicioVencimentoInputDate').clear().type('12/09/2023')
        cy.get('#form\\:fimVencimentoInputDate').clear().type('12/09/2023')
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
    }

    filtroEvento() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').should('be.visible')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').click()
        cy.get('#form\\:nomeEvento').should('be.visible')
        cy.get('#form\\:nomeEvento').select('6')
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')

    }

    filtroPlano() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').should('be.visible')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').click()
        cy.get('#form\\:nomePlano').should('be.visible')
        cy.get('#form\\:nomePlano').select('FAZ TUDO')
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
    }

    filtroSituacao() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').should('be.visible')
        cy.get('#form\\:j_id_jsp_1330362801_59_header').click()
        cy.get('[name="form:j_id_jsp_1330362801_132"]').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
    }

    filtroModalidade() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:modalidadesGeralCliente_header').should('be.visible')
        cy.get('#form\\:modalidadesGeralCliente_header').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')

    }

    filtroEmpresa() {
        this.loginInicio()
        cy.visit('faces/listasRelatorios.jsp')
        cy.get('#form\\:GeralEmpresas_header').should('be.visible')
        cy.get('#form\\:GeralEmpresas_header').click()
        cy.get('[style=""] > input').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
        cy.get('#form\\:consultarGeralClientes').click()
        cy.get('#form\\:consultarGeralClientes').should('be.visible')
    }

    historicoPontos() {
        this.loginInicio()
        cy.visit('faces/relatorio/historicoPontosRel.jsp')
        cy.wait(1000)
        cy.get('#form\\:selecionarDetalhado').should('be.visible')
        cy.get('#form\\:selecionarDetalhado').click()
        cy.get('#form\\:selecionarcheckAlunosAtivos').click({ force: true })
        cy.get('#form\\:nomeCliente').should('be.visible')
        cy.get('#form\\:nomeCliente').type('AA_PRIMEIRO CLIENTE')
        cy.get('#form\\:gerarRelatorioPontos').should('be.visible')
        cy.get('#form\\:gerarRelatorioPontos').click()
        cy.contains('Refazer Consulta')
    }

    relatorioSaldoCliente() {
        this.loginInicio()
        cy.visit('faces/relatorio/saldoCreditoRel.jsp')
        cy.get('#form\\:empresa').should('be.visible')
        cy.get('#form\\:empresa').select('0')
        cy.get('#form\\:imprimir').should('be.visible')
        cy.get('#form\\:imprimir').click()
        cy.contains('NOME')
    }

    grupoRiscoMostrarVinculos() {
        this.loginInicio()
        cy.get('.MENU_ADM_BUSINESS_INTELLIGENCE_ZW_UI_NAO_TRABALHA_COM_PONTUCAO').click()
        cy.wait(5000)
        cy.get('#form\\:btnMostrarVinculos1').click()
        cy.capturaPopUp()
        cy.get('#form\\:totalRisco').should('be.visible')
        cy.get('#form\\:totalRisco').should('contain', 'Total 0 itens')
        cy.contains('Churn Prediction - Clientes com peso 6 ')
    }

    grupoRiscoDemonstrativo() {
        this.loginInicio()
        cy.get('.MENU_ADM_BUSINESS_INTELLIGENCE_ZW_UI_NAO_TRABALHA_COM_PONTUCAO').click()
        cy.wait(5000)
        cy.get('#form\\:containerRisco > .bi-header > .bi-panel > .bi-titulo').should('be.visible')
    }

    consultarFrequenciaeOcupacaoTurmas() {
        cy.visit('faces/relatorio/clientePorAniversarioRel.jsp')
        cy.wait(2500)
        cy.get('#formDescobridor\\:userKey').type('teste')
        cy.get('#formDescobridor\\:userName').type('admin')
        cy.get('#formDescobridor\\:userPassword').type('123')
        cy.get('#formDescobridor\\:descobrir').click()
        cy.wait(3000)
        cy.get('#formModulo\\:linkAbrirZillyon').click()
        cy.wait(4000)

        cy.get('.zw_pesquisa_global').type('Frequência e Ocupação de Turmas')
        cy.wait(2000)
        cy.get('.richfaces_suggestionEntry > :nth-child(2)').click()
        cy.capturaPopUp()
        cy.wait(5000)
        cy.intercept('POST', '**/frequenciaOcupacaoTurmas.jsp').as('formUpdate');
        cy.get('#form\\:empresa').should('be.visible').select('1');
        cy.wait('@formUpdate');
        cy.wait(2000)
        cy.get('select#form\\:modalidade').should('be.visible').select('TESTE AUTO TURMA');
        cy.wait(2000)
        cy.wait('@formUpdate');
        cy.get('select#form\\:turma').should('be.visible').select('TESTE AUTO');
        cy.wait(2000)
        cy.wait('@formUpdate');
        cy.get('select#form\\:professor').should('be.visible').select('ADRIANA COSTA');
        cy.wait(2000)
        cy.wait('@formUpdate');
        cy.get('select#form\\:ambiente').should('be.visible').select('TESTE AUTO');
        cy.wait(2000)
        cy.get('[id="form:consultar"]').click();
        cy.get('.mensagem').should('contain', 'Dados Consultados com Sucesso');
    }

    pesquisaPorData() {
        this.loginInicio()
        this.pesquisaNaLupa('Relatório de Clientes')
        cy.get('[id="form:empresa"]').should('be.visible')
        cy.get('[id="form:empresa"]').select('1')
        cy.get('[id="form:consultaRelatorio"]').should('be.visible')
        cy.get('[id="form:consultaRelatorio"]').click()
        cy.get('[class="tituloboxcentro"]').should('be.visible')
        cy.get('[class="tituloboxcentro"]').should('contain', 'Resultados')
    }

    pesquisaPorSituacao() {
        this.loginInicio()
        this.pesquisaNaLupa('Relatório de Clientes')

        cy.get('[id="form:abaSituacoes_lbl"]').should('be.visible')
        cy.get('[id="form:abaSituacoes_lbl"]').click()
        cy.get('[id="visitantesC"]').click()
        cy.get('[id="form:consultaRelatorio"]').should('be.visible')
        cy.get('[id="form:consultaRelatorio"]').click()
        cy.get('[class="tituloboxcentro"]').should('be.visible')
        cy.get('[class="tituloboxcentro"]').should('contain', 'Resultados')

    }

    pesquisaPorPlanos() {
        this.loginInicio()
        this.pesquisaNaLupa('Relatório de Clientes')

        cy.get('[id="form:abaPlanos_lbl"]').should('be.visible')
        cy.get('[id="form:abaPlanos_lbl"]').click()
        cy.get('#form\\:abaPlanos > table > tbody > tr > td > fieldset > input[type=checkbox]:nth-child(2)').check()
        cy.get(':nth-child(4) > :nth-child(2) > fieldset > legend > input').check()
        cy.get('[id="duracao2FAZ TUDO"]').check()
        cy.get('[id="form:consultaRelatorio"]').should('be.visible')
        cy.get('[id="form:consultaRelatorio"]').click()
        cy.get('[class="tituloboxcentro"]').should('be.visible')
        cy.get('[class="tituloboxcentro"]').should('contain', 'Resultados')

    }

    pesquisaPorColaboradores() {
        this.loginInicio()
        this.pesquisaNaLupa('Relatório de Clientes')

        cy.get('[id="form:abaColaboradores_lbl"]').should('be.visible')
        cy.get('[id="form:abaColaboradores_lbl"]').click()
        cy.get('[id="colaboradorCConsultor"]').check()
        cy.get('[id="form:consultaRelatorio"]').should('be.visible')
        cy.get('[id="form:consultaRelatorio"]').click()
        cy.get('[class="tituloboxcentro"]').should('be.visible')
        cy.get('[class="tituloboxcentro"]').should('contain', 'Resultados')

    }

    pesquisaPorColunas() {
        this.loginInicio()
        this.pesquisaNaLupa('Relatório de Clientes')

        cy.get('[id="form:escolherColunas_lbl"]').should('be.visible')
        cy.get('[id="form:escolherColunas_lbl"]').click()
        cy.get('#form\\:colunasAgrupamento > table > tbody > tr > td:nth-child(3) > span:nth-child(8)').click()
        cy.get('[id="form:consultaRelatorio"]').should('be.visible')
        cy.get('[id="form:consultaRelatorio"]').click()
        cy.get('[class="tituloboxcentro"]').should('be.visible')
        cy.get('[class="tituloboxcentro"]').should('contain', 'Resultados')

    }

    cadastrarVisitante(nomeAluno) {
        cy.get(this.elements.adicionarCliente).click({ force: true })
        cy.get(this.elements.termosConsulta).should('be.visible').clear().type(nomeAluno);

        cy.get(this.elements.botaoConsultarCliente).click({ force: true })
        cy.get(this.elements.botaoNovo).click({ force: true })
        cy.get(this.elements.botaoSalvar).click({ force: true })
        cy.get(this.elements.consultor).select('PACTO - MÉTODO DE GESTÃO');
        cy.get(this.elements.cadastrarVisitante).click({ force: true })
        cy.get(this.elements.botaoGravar).click({ force: true })
        cy.wait(1500)

    }

    lancarPlano(nomeAluno) {
        cy.get(this.elements.btnNovoContrato).should('exist').and('be.visible').click({ force: true })
        cy.get(this.elements.planoSelect).should('exist').and('be.visible').select('FAZ TUDO');
        cy.get(this.elements.marcarDuracao).should('exist').and('be.visible').click({ force: true })
        cy.wait(1500)
        cy.get(this.elements.conferirNegociacao).should('exist').and('be.visible').click();
        cy.get(this.elements.botaoConfirmar, { timeout: 60000 }).should('exist').and('be.visible').click();
        cy.get(this.elements.senhaAutorizacao).should('exist').and('be.visible').type('123');
        cy.get(this.elements.botaoAutorizar).should('exist').and('be.visible').click();

    }

    validarBI(nomeAluno) {

        cy.wait(5000)
        this.pesquisaNaLupa("BI Administrativo")
        cy.get(this.elements.biHeaderConversao, { timeout: 10000 }).should('exist').scrollIntoView().trigger('mouseover')
        cy.get(this.elements.icvContainerNumeros, { timeout: 10000 }).should('exist').click();
        cy.capturaPopUp()
        cy.wait(3400)
        cy.get(this.elements.fundoBranco).contains(nomeAluno.replace('.', '').toUpperCase());

    }


    ConverterVendas(nomeAluno) {
        this.loginInicioPactoBr()
        this.cadastrarVisitante(nomeAluno)
        this.lancarPlano(nomeAluno)
        cy.visit('faces/inicio.jsp')
        cy.get('#fmLay\\:usernameLoginZW').type('pactobr')
        cy.get('#fmLay\\:pwdLoginZW').type('123')
        cy.get('#fmLay\\:btnEntrar').click()
        cy.get(':nth-child(1) > a > .lgmodule > img').click()
        cy.wait(2300)
        this.validarBI(nomeAluno)
    }

    preencherMatricula(matricula) {
        cy.get(this.elements.matriculaInput).clear().type(matricula);
        cy.get('body').click(); // Força o registro
    }

    clicarRegistrarAcessos() {
        cy.get(this.elements.registrarAcessosButton).should('be.visible').click({ force: true });
        cy.get(this.elements.registrarAcessosModalButton).should('be.visible').click({ force: true });
    }

    validarMensagemDeSucesso(matricula) {
        cy.get(this.elements.modalMensagem)
            .should('contain.text', `Último acesso gravado para MAT ${matricula}`);
    }


    CadastrarAlunoGestaoDeAcesso(matricula) {
        this.loginInicioPactoBr()
        cy.visit('faces/relatorio/registrarAcessoAvulso.jsp');
        this.preencherMatricula(matricula)
        this.clicarRegistrarAcessos()
        this.validarMensagemDeSucesso(matricula)
    }

    validarHeaderBI() {
        cy.get(this.elements.biHeader)
            .scrollIntoView()
            .should('exist')
            .trigger('mouseover');
    }

    clicarEmAcessosHoje() {
        this.elements.acessosHojeLink().click({ force: true });
    }

    validarPopup() {
        cy.capturaPopUp()
    }

    validarAluno(texto) {
        cy.get('body', { timeout: 10000 }).should('be.visible').and('contain.text', texto);
    }


    validarBIGestaoAcessos(aluno) {
        this.loginInicioPactoBr()
        this.pesquisaNaLupa('BI Administrativo')
        this.validarHeaderBI()
        this.clicarEmAcessosHoje()
        this.validarPopup()
        this.validarAluno(aluno)
    }

    validarTituloTicketMedio() {
        cy.get(this.elements.biHeader, { timeout: 10000 })
            .scrollIntoView()
            .should('exist')
            .trigger('mouseover');
    }

    abrirFiltroTicketMedio() {
        cy.get(this.elements.filtroTicketMedioButton, { timeout: 10000 }).should('be.visible').click();
    }

    validarTituloDesconsiderar() {
        cy.get(this.elements.tituloDesconsiderar)
            .should('be.visible')
            .should('have.text', 'Desconsiderar alunos inadimplentes:', { timeout: 10000 });
    }

    marcarCheckboxInadimplentes() {
        cy.get(this.elements.checkboxInadimplentes, { timeout: 10000 }).should('be.visible').check();
    }

    clicarAtualizarTM() {
        cy.get(this.elements.atualizarTMButton, { timeout: 10000 }).should('be.visible').click();
        cy.wait(300) //pausa para tela carregar
    }

    atualizarTicketMedio() {
        cy.get(this.elements.consultarTicketButton, { timeout: 10000 })
            .should('be.visible')
            .click({ force: true });
        cy.wait(300) //pausa para tela carregar
    }

    validarTicketMedio() {
        cy.get(this.elements.containerTicketMedio, { timeout: 10000 })
            .should('be.visible')
            .and('contain', 'Ticket Médio');
    }


    ValidarBiGestaodeAcesso() {
        this.loginInicioPactoBr()
        this.pesquisaNaLupa('BI Administrativo')
        this.validarTituloTicketMedio()
        this.abrirFiltroTicketMedio()
        this.validarTituloDesconsiderar()
        this.marcarCheckboxInadimplentes()
        this.clicarAtualizarTM()
        this.atualizarTicketMedio()
        this.validarTicketMedio()
    }

    localizarControleOperacoes() {
        cy.get(this.elements.tituloTicketMedio, { timeout: 10000 })
            .scrollIntoView()
            .should('exist', { timeout: 10000 })
            .trigger('mouseover', { timeout: 10000 });

        cy.get(this.elements.tituloControleOperacoes)
            .should('have.text', 'Controle de Operações de Exceções', { timeout: 10000 });
        cy.wait(500);
    }

    abrirControleOperacoes() {
        cy.get(this.elements.botaoAbrirControleOperacoes, { timeout: 10000 }).click();
        cy.wait(300);
    }

    botaoVerMais() {

        cy.get(this.elements.botaoRenegociacoesParcelas).should('exist', { timeout: 10000 })
            .click({ force: true })
        cy.wait(500);


    }

    clicarOpcaoNoControle(opcao) {
        cy.contains(opcao)
            .parent()
            .parent()
            .find('a')
            .click({ force: true });
        cy.capturaPopUp()
    }

    validarTituloRelatorio(titulo) {
        cy.get(this.elements.headerRelatorio).should('contain', titulo, { timeout: 10000 });
        cy.wait(500);
    }

    validarColunasNoRelatorio(colunas) {
        colunas.forEach((coluna) => {
            cy.get(this.elements.tabelaResultados).should('contain', coluna, { timeout: 10000 });
            cy.wait(500);
        });
    }

    ValidarColunaParcelasCanceladas() {
        this.loginInicioPactoBr()
        this.pesquisaNaLupa('BI Administrativo')
        this.localizarControleOperacoes()
        this.abrirControleOperacoes()
        this.botaoVerMais()
        this.clicarOpcaoNoControle('Parcelas Canceladas')
        this.validarTituloRelatorio('Parcelas Canceladas')
        this.validarColunasNoRelatorio(['Tipo de Operação', 'Justificativa']);
    }

    ValidarColunaResumoDasNegociacoes() {
        this.loginInicioPactoBr()
        this.pesquisaNaLupa('BI Administrativo')
        this.localizarControleOperacoes()
        this.abrirControleOperacoes()
        this.botaoVerMais()
        this.clicarOpcaoNoControle('Renegociações de parcelas')
        this.validarTituloRelatorio('Resumo das renegociações de parcelas')
        this.validarColunasNoRelatorio(['Justificativa']);
    }

}

export default new RelatorioPage();
