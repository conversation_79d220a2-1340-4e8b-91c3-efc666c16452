import DateUtils from "@utils/DateUtils"
import EnvUtils from "@utils/EnvUtils"

class NovoCaixaAbertoPage {
    elements = {
        INPUT_BUSCA_RAPIDA: '#input-busca-rapida',
        TABELA_CLIENTE: '.table-content',
        CHECK_TODAS_PARCELAS_ALUNO: '#ds3-checkbox__custom-table-row-0-cell-cliente-checkbox-group',
        SELECT_CONVENIO: 'ds3-select[valuekey="codigo"]',
        DS3_SELECT_OPTIONS: '.ds3-select-options',
        BOTAO_RECEBER: '#caixa-em-aberto-btn-receber',
        BOTAO_RECEBER_CAIXA_ABERTO: '#caixa-em-aberto-btn-receber',
        SELECT_FORMA_PAGAMENTO: 'ds3-select[placeholder="Selecione a forma de pagamento"]',
        MODAL_FINALIZAR: '#finalizar',
        SELECIONAR_FORMA_PAGAMENTO: 'span.ds3-select-value-placeholder',
        SELECIONAR_ADQUIRENTE: '#carta-credito-offline-0-select-adquirente > .ds3-select',
        SELECIONAR_ADQUIRENTE_DEBITO: '.cartao-de-debito > :nth-child(1) > .ds3-form-field > .content > .input-area',
        SELECIONAR_OPERADORA: '#carta-credito-offline-0-select-operadora > .ds3-select',
        SELECIONAR_OPERADORA_DEBITO: '#carta-debito-0-select-operadora > .ds3-select',
        SELECIONAR_PARCELA: '#pagamento-online-0-input-nr-parcelas > .ds3-select',
        SELECIONAR_PARCELA_OFFLINE: '#carta-credito-offline-0-input-nr-parcelas > .ds3-select',
        AUTORIZACAO: '#carta-credito-offline-0-input-autorizacao',
        AUTORIZACAO_DEBITO: '#carta-debito-0-input-autorizacao',
        FORMA_PAG_DEBITO: ':nth-child(6) > .ds3-option',
        SELECIONAR_CONVENIO_PIX: '.ds3-select-value-placeholder',
        INPUT_VALOR: '#forma-pagamento-valor-0',
        BOTAO_RECEBER_FINALIZAR: '#receber-parcelas-btn-receber',
        BOTAO_IR_TELA_ALUNO: '#receber-parcelas-finalizar-btn-tela-aluno',
        SELECT_BANCO_CHEQUE: '#cheque-0-input-banco > .ds3-select',
        INPUT_AGENCIA: '#cheque-0-input-agencia',
        INPUT_CONTA: '#cheque-0-input-conta',
        INPUT_NUMERO_CONTA: '#cheque-0-input-numero',
        BOTAO_GERAR_CHEQUE: '#cheque-0-btn-gerar-cheques',
        CANCELAR_PARCELAS: '#caixa-em-aberto-btn-cancelar',
        JUSTIFICATIVA_CANCELAR: '[id="cancelar-parcelas-justificativa"]',
        CONFIRMAR_CANCELAMENTO: '[id="cancelar-parcelas-btn-confirmar"]',
        MENSAGEM_RETORNO: '.snotifyToast__body',
        CREDITO_SIM: '#creditosim',
        TITULAR_CARTAO: '#pagamento-online-0-input-titular',
        DOCUMENTO_CARTAO: '#pagamento-online-0-input-documento',
        NUMERO_CARTAO: '#pagamento-online-0-input-nr-card',
        VALIDADE_CARTAO: '#pagamento-online-0-input-validade',
        CVV_CARTAO: '#pagamento-online-0-input-cvv',
        ABA_PACTOPAY: '#pcc-tab-pactopay',
        VOLTAR: '#go-back-2 > .pct',
        MENSAGEM_ALERTA: '#receber-parcelas-finalizar-msg-erro',
        EDITAR_PARCELA: '#receber-parcelas-editar',
        SELECIONA_CALENDARIO: '.ds3-input-date-wrapper',
        SELECIONA_DATA: '#mat-datepicker-0',
        CALENDAR: '.mat-calendar-content td',
        CAMPO_DATA_CALENDAR: '.wrapper > .ng-valid',
        MENSAGEM_ALTERAR_DATA: '.fixed-toast-body',
        SALVAR_EDICAO_DATA_PAGAMENTO: '#receber-parcelas-editar-btn-salvar',
        INDICADORES: '.indicadores',
        SELECIONAR_TODOS_CLIENTES: '#custom-table-head-cliente-checkbox-everything',
        TITULO: '.titulo',
        CAMPO_CLIENTE: '#custom-table-row-0-cell-cliente-btn-name',
        EXPANDIR_PARCELA_CLIENTE: '#custom-table-row-0-cell-cliente-btn-expand',
        SELECIONA_PARCELA_CLIENTE: '#custom-table-row-0-expanded-entry-0-cell-checkbox',
        IMPRIMIR_CONTRATO: 'button:has(i[ds3tooltip="Imprimir contrato"])',
        VALOR_BOLETO: '#gerar-boleto-input-valor',
        BOTAO_GERAR_BOLETO: '#caixa-em-aberto-btn-gerar-boleto',
        BOTAO_IMPRIMIR_CAIXA_ABERTO: '#custom-table-row-0-cell-acoes-btn-print',
        BOTAO_RENEGOCIAR: '#caixa-em-aberto-btn-renegociar',
        DATA_CALENDARIO: '.wrapper input',
        DATA_CALENDARIO_INPUT: 'input[placeholder="dd/mm/aaaa"]',
        COLUNA_DATA_VENCIMENTO_BOLETO: ':nth-child(2) > .column-cell',
        CHECKM_MULTA_BOLETO: '#ds3-checkbox__gerar-boleto-checkbox-cobrar-multa-juros',
        TOTAL_PARCELAS: 'div.total-das-parcelas.ng-star-inserted',
        COMPARTILHAR_BOLETO: '#gerar-boleto-btn-compartilhar-boleto',
        QR_CODE: '.img-qrcode > img',
        EMAIL: '.form-group > #compartilhar-boleto-email',
        BOTAO_COMPARTILHAR_EMAIL: '.div-compartilhar-email > .pct',
        BOTAO_COPIAR_LINK: '[data-cy="btn-copiar-link"] > .content',
        DATA_CALENDARIO_ABERTO: '.calendar-label',
        SETA_VOLTAR: 'div.type-h4-bold.icon-voltar.ng-star-inserted',
        INSERIR_SENHA: '#aut-input-psw',
        CONFIRMAR_SENHA: '#aut-btn-confirm',
        SELECT_TIPO_IDENTIFICACAO: '#cheque-0-select-tipo-identificacao > .ds3-select',
        INPUT_CPF: '#cheque-0-input-documento',
        DIA_VENCIMENTO_CALENDAR: '.mat-calendar-body-active > .mat-calendar-body-cell-content',
        INPUT_CODIGO_BANCO: '.cheque > :nth-child(1) > .ds3-form-field > .content > .input-area',
        TITULO_PAGINA: '.type-h3-bold',
        BOTAO_RECEBER_NEGOCIACAO: '[data-cy="btn-receber"] > .content',
        INPUT_RECEBER_NEGOCIACAO: '.escolha-cliente > :nth-child(1)',
        PRIMEIRA_BUSCA_NEGOCIACAO: 'table > :nth-child(2) > :nth-child(1)',
        IMPRIMIR_CONTRATO_RECEBER: '#receber-parcelas-btn-imprimir-contrato > .pct',
        ENVIAR_CONTRATO_EMAIL: '#receber-parcelas-btn-enviar-contrato > .pct',
        SELECIONAR_CONTRATO: '#select-contrato > .ds3-select > .ds3-select-body > .ds3-select-value > .ds3-select-value-placeholder',
        PRIMEIRA_OPCAO_SELECIONAR_CONTRATO: '.ds3-option',
        ADICIONAR_EMAIL: '#enviar-contrato-email-novo-email',
        BOTAO_ENVIAR_EMAIL: '[data-cy="enviar-contrato-email-btn-enviar"] > .content',
        RENOVAR_CONTRATO: '[data-cy="btn-sim"] > .content',
        NOME_CONTRATO: ':nth-child(1) > span '

    }

    private senha(): void {
        cy.get(this.elements.INSERIR_SENHA).type('123')
        cy.get(this.elements.CONFIRMAR_SENHA).click()
    }

    private preencherCampoData(data: string): void {
        // Tenta diferentes seletores para o campo de data
        const seletores = ['input[placeholder="dd/mm/aaaa"]', 'input[matinput][placeholder="dd/mm/aaaa"]', '.wrapper input[placeholder="dd/mm/aaaa"]', 'input[ds3input][placeholder="dd/mm/aaaa"]', '.ds3-input-date-wrapper', this.elements.DATA_CALENDARIO_INPUT]

        let campoEncontrado = false

        seletores.forEach((seletor, index) => {
            if (!campoEncontrado) {
                cy.get('body').then($body => {
                    if ($body.find(seletor).length > 0) {
                        cy.get(seletor).first().then($el => {
                            if ($el.is(':visible') && !$el.is(':disabled')) {
                                cy.wrap($el).scrollIntoView()
                                cy.wrap($el).click({ force: true })
                                cy.wrap($el).clear({ force: true })
                                cy.wrap($el).type(data, { force: true })
                                campoEncontrado = true
                                cy.log(`Campo de data encontrado com seletor: ${seletor}`)
                            }
                        })
                    }
                })
            }
        })
    }

    buscarCliente(nomeCliente: string): void {
        cy.intercept('GET', '**/caixa-aberto/consultar*').as('listaClientes')
        cy.wait(2000)
        cy.get(this.elements.INPUT_BUSCA_RAPIDA).clear({ force: true }).type(nomeCliente, { delay: 0, force: true })
        cy.wait('@listaClientes', { timeout: 10000 }).wait(1500)
    }

    selecionarTodasParcelasCliente(nomeCliente: string): void {
        this.buscarCliente(nomeCliente)
        cy.wait(1500)
        cy.get(this.elements.CHECK_TODAS_PARCELAS_ALUNO).check({ force: true })
    }

    gerarBoletoParcela(nomeCliente: string): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        cy.get('.divtabs').contains('Boleto por data de vencimento das parcelas').click()
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.contains('Boleto gerado com sucesso', { timeout: 60000 }).should('be.visible')
    }

    gerarBoletoUnico(nomeCliente: string): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.contains('Boleto gerado com sucesso').should('be.visible')

        this.acessarTelaDoAlunoLupa(nomeCliente)

        cy.get(this.elements.ABA_PACTOPAY).click()

        cy.wait(3000)

        // Valida o valor total do boleto
        cy.contains('R$ 970,80').trigger('mouseover').should('be.visible')

    }

    receberEmDinheiro(nomeCliente: string, usaSenha: boolean = false) {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.BOTAO_RECEBER_CAIXA_ABERTO).click()
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Dinheiro').click()
        cy.contains('button', 'Receber').click()

        if (usaSenha) this.senha() ?? null;

        cy.get(this.elements.MODAL_FINALIZAR, { timeout: 20000 }).contains('Pagamento realizado com sucesso')
        cy.contains('button', 'Ir para o perfil do aluno ').click()
    }

    imprimirContratoEnviarEmail(nomeCliente: string, usaSenha: boolean = false) {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Dinheiro').click()
    }

    receberParcelasDinheiro(): void {
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Dinheiro').click()
        cy.contains('button', 'Receber').click()

        cy.get(this.elements.MODAL_FINALIZAR, { timeout: 20000 }).contains('Pagamento realizado com sucesso')
        cy.get(this.elements.BOTAO_IR_TELA_ALUNO).click()
    }

    receberParcelaBoletoPendente(nomeCliente: string) {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.contains('Boleto gerado com sucesso').should('be.visible')

        cy.get(this.elements.VOLTAR).click()
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.BOTAO_RECEBER_CAIXA_ABERTO).click()

        cy.contains('Existem boletos pendentes.').should('be.visible')
    }

    receberEmCheque(nomeCliente: string) {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.BOTAO_RECEBER_CAIXA_ABERTO).click()
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Cheque').click()
        cy.get(this.elements.INPUT_CODIGO_BANCO).type('237')
        cy.get(this.elements.INPUT_AGENCIA).type('1234')
        cy.get(this.elements.INPUT_CONTA).type('12345')
        cy.get(this.elements.INPUT_NUMERO_CONTA).type('1')
        cy.get(this.elements.SELECT_TIPO_IDENTIFICACAO).click().wait(1500)
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('CPF').click({ force: true })
        cy.get(this.elements.INPUT_CPF).type('92969775840')
        cy.get(this.elements.BOTAO_GERAR_CHEQUE).click()
        cy.get(this.elements.BOTAO_RECEBER_FINALIZAR).click()
        cy.get(this.elements.INSERIR_SENHA).type('123')
        cy.get(this.elements.CONFIRMAR_SENHA).click()
        this.acessarTelaAluno()
    }

    receberCartaoOffline(nomeCliente: string, tipo: 'credito' | 'debito'): void {
        this.selecionarTodasParcelasCliente(nomeCliente);
        cy.wait(3000);
        cy.contains('button', 'Receber').click();
        cy.get(this.elements.SELECIONAR_FORMA_PAGAMENTO).click();

        if (tipo === 'credito') {
            cy.contains(' Cartão De Crédito - OFFLINE ').click();
            cy.get(this.elements.SELECIONAR_ADQUIRENTE, { timeout: 3000 }).click();
            cy.contains('BIN').click();
            cy.get(this.elements.SELECIONAR_OPERADORA, { timeout: 3000 }).should('be.visible').click();
            cy.contains('AMERICAN EXPRESS (CREDITO)').click();
            cy.get(this.elements.SELECIONAR_PARCELA_OFFLINE, { timeout: 3000 }).should('be.visible').click();
            cy.contains('Á Vista').click();
            cy.get(this.elements.AUTORIZACAO).type('123');
        } else {
            cy.contains('Cartão De Débito').click();
            cy.get(this.elements.SELECIONAR_ADQUIRENTE_DEBITO, { timeout: 3000 }).click();
            cy.contains('BIN').click();
            cy.get(this.elements.SELECIONAR_OPERADORA_DEBITO, { timeout: 3000 }).should('be.visible').click();
            cy.contains('ELO (DEBITO)').click();
            cy.get(this.elements.AUTORIZACAO_DEBITO).type('123');
        }

        cy.contains('button', 'Receber').click();
        cy.get(this.elements.INSERIR_SENHA).type('123')
        cy.get(this.elements.CONFIRMAR_SENHA).click()
        cy.get(this.elements.MODAL_FINALIZAR).contains('Pagamento realizado com sucesso');
    }

    acessarTelaAluno(): void {
        cy.get(this.elements.BOTAO_IR_TELA_ALUNO).click()

    }

    acessarTelaDoAlunoLupa(nomeAluno: string): void {
        cy.intercept('GET', '**/contratos/by-matricula/**').as('getContratos');

        cy.get('#module-ntr', { timeout: 60000 }).click();
        cy.get('#topbar-search-field').type(nomeAluno);

        cy.contains(nomeAluno).click();
        cy.wait('@getContratos');
    }

    receberEmPix(nomeCliente: string, semSenha: boolean = true) {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.BOTAO_RECEBER).click()
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Pix').click()
        cy.get(this.elements.SELECIONAR_CONVENIO_PIX).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('PIX PRODUCAO CAIXA ABERTO').click()
        if (!semSenha) {
            cy.get(this.elements.INSERIR_SENHA).type('123')
            cy.get(this.elements.CONFIRMAR_SENHA).click()
        }
        cy.wait(3000);
        cy.contains('Pix gerado com sucesso', { timeout: 3000 })
        cy.wait(3000);
        cy.contains('Compartilhar Pix', { timeout: 3000 }).click()
        cy.contains('Copiar código pix').should('be.visible');
        cy.contains('Copiar Link pagamento').should('be.visible');
        cy.contains('Enviar por WhatsApp').should('be.visible');
        cy.contains('Link por email').should('be.visible');


    }

    receberValorSuperiorAdiconandoCredito(nomeCliente: string): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.BOTAO_RECEBER_CAIXA_ABERTO).click()
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Dinheiro').click()
        cy.get(this.elements.INPUT_VALOR).clear({ force: true }).type('100000')
        cy.get(this.elements.BOTAO_RECEBER_FINALIZAR).click()
        cy.get('#mdl-add-confirmar-btn-sim').click()
        cy.get('#aut-input-psw').type('123')
        cy.get('#aut-btn-confirm').click()
        cy.get(this.elements.MODAL_FINALIZAR, { timeout: 10000 }).contains('Pagamento realizado com sucesso');
        this.acessarTelaAluno()
    }

    receberParcelaUsandoSaldoCredito(nomeCliente: string, usaSenha: boolean = false): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.get(this.elements.BOTAO_RECEBER_CAIXA_ABERTO).click().wait(2000)
        cy.get('body').click()
        cy.get(this.elements.CREDITO_SIM).click()

        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('Dinheiro').click()

        cy.get(this.elements.BOTAO_RECEBER_FINALIZAR).click()

        if (usaSenha) this.senha() ?? null

        cy.get(this.elements.MODAL_FINALIZAR, { timeout: 10000 }).contains('Pagamento realizado com sucesso');

    }

    receberCartaoOnline(nomeCliente: string, usaSenha: boolean = true): void {
        this.selecionarTodasParcelasCliente(nomeCliente);
        cy.wait(3000);
        cy.contains('button', 'Receber').click();
        cy.get(this.elements.SELECIONAR_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains(' Cartão De Crédito - ONLINE ').click()
        cy.wait(3000);
        cy.get('#pagamento-online-0-select-convenio > .ds3-select').click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('CIELO ONLINE SANDBOX').click()
        cy.get(this.elements.TITULAR_CARTAO).type(nomeCliente);
        cy.get(this.elements.DOCUMENTO_CARTAO).type('25813732101');
        cy.get(this.elements.NUMERO_CARTAO).type('5571566758543951');
        cy.get(this.elements.VALIDADE_CARTAO).type('05/30');
        cy.get(this.elements.CVV_CARTAO).type('123');
        cy.get(this.elements.SELECIONAR_PARCELA).click();
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('1X- À vista').click()
        cy.get(this.elements.BOTAO_RECEBER_FINALIZAR).click()
        if (usaSenha) {
            cy.get(this.elements.INSERIR_SENHA).type('123')
            cy.get(this.elements.CONFIRMAR_SENHA).click()
        }
        cy.get(this.elements.MODAL_FINALIZAR, { timeout: 10000 }).contains('Pagamento realizado com sucesso');
        this.acessarTelaAluno()
    }

    acessarTelaNova(): void {
        cy.wait(3000);
        const url = cy.url().should('include', 'tela8.jsp')

        if (url) cy.visitarCaixaAberto() ?? null
    }

    cancelarParcelas(nomeCliente): void {
        this.selecionarTodasParcelasCliente(nomeCliente);
        cy.get(this.elements.CANCELAR_PARCELAS).click()
        cy.wait(3000);
        cy.get(this.elements.JUSTIFICATIVA_CANCELAR).type('TESTE CANCELAR')
        cy.get(this.elements.CONFIRMAR_CANCELAMENTO).click()
        cy.get(this.elements.MENSAGEM_RETORNO).should('be.visible').contains('Parcelas canceladas com sucesso!')
        cy.get(this.elements.TABELA_CLIENTE).should('be.visible').contains('Nenhum item encontrado')


    }

    alterarDataRecebimento(nomeCliente: string, diasParaDiminuirNoCalendario: number): void {
        const data = DateUtils.futureDateBR(-30)
        this.selecionarTodasParcelasCliente(nomeCliente);
        cy.contains('button', 'Receber').click();
        cy.wait(3000);

        const dataAlvo = new Date();
        dataAlvo.setDate(dataAlvo.getDate() - diasParaDiminuirNoCalendario);

        cy.get(this.elements.EDITAR_PARCELA).click();
        cy.wait(3000);

        // cy.get(this.elements.SELECIONA_CALENDARIO).click();
        // cy.get('.mat-calendar-content').contains('1').click().wait(2000)

        cy.get('.ds3-input-date-wrapper > .ng-untouched').clear({ force: true }).type(data.replace(/\//g, ''), { force: true })

        cy.get(this.elements.MENSAGEM_ALTERAR_DATA, { timeout: 10000 })
            .contains('Alterar a data de pagamento modificará todos os relatórios e estatísticas do sistema relacionados à data selecionada. Confirme essa ação apenas se tiver certeza.');

        cy.get(this.elements.SALVAR_EDICAO_DATA_PAGAMENTO).click();
    }

    validacaoErro(formaPagamento, textoEsperado): void {
        ///teste se inicia na forma de pagamento, tem o intuito de validar alguma mensagem de erro/alerta
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains(formaPagamento).click()
        cy.contains('button', 'Receber').click()
        this.inserirSenha()
        cy.get(this.elements.MENSAGEM_ALERTA, { timeout: 20000 }).should('contain.text', textoEsperado);


    }

    cancelarParcelaBoletoPendente(nomeCliente): void {
        this.selecionarTodasParcelasCliente(nomeCliente);
        cy.get(this.elements.CANCELAR_PARCELAS).click()
        cy.wait(3000);
        cy.get(this.elements.TITULO, { timeout: 20000 }).should('be.visible').should('contain.text', 'Boletos pendentes')
        cy.contains('Existe boleto pendente para as parcelas selecionadas. Deseja cancelar os boletos?').trigger('mouseover').should('be.visible')
        cy.contains('Sim, cancelar boletos').trigger('mouseover').should('be.visible').click()
        cy.contains('Boletos cancelados com sucesso!', { timeout: 20000 }).should('be.visible')

    }

    selecionarTodosClientes(): void {
        cy.wait(3000);
        cy.get(this.elements.INPUT_BUSCA_RAPIDA).clear();
        cy.wait(3000);
        // Valida ANTES DO CLIQUE (deve ser zero)
        cy.get(this.elements.INDICADORES).invoke('text').then((text) => {
            const cleanText = text.replace(/\s+/g, '');

            const parcelaMatch = cleanText.match(/Parcelas?(\d+)/);
            expect(parcelaMatch, 'Encontrou número de parcelas antes do clique').to.not.be.null;

            const numParcelas = parseInt(parcelaMatch[1], 10);
            expect(numParcelas, 'Número de parcelas antes do clique').to.equal(0);
        });

        // Realiza o clique
        cy.get(this.elements.SELECIONAR_TODOS_CLIENTES).should('be.visible').click();

        // Valida DEPOIS DO CLIQUE (deve ser maior que zero)
        cy.get(this.elements.INDICADORES).invoke('text').then((text) => {
            const cleanText = text.replace(/\s+/g, '');

            const parcelaMatch = cleanText.match(/Parcelas?(\d+)/);
            expect(parcelaMatch, 'Encontrou número de parcelas depois do clique').to.not.be.null;

            const numParcelas = parseInt(parcelaMatch[1], 10);
            expect(numParcelas, 'Número de parcelas depois do clique').to.be.greaterThan(0);
        });


    }

    selecionarParcelaCliente(nomeCliente, quantidadeParcela, valorParcela): void {
        this.buscarCliente(nomeCliente);

        function extrairIndicadores(text) {
            const cleanText = text.replace(/\s+/g, '');
            const parcelaMatch = cleanText.match(/Parcelas?(\d+)/);
            const valorMatch = cleanText.match(/R\$([\d.,]+)/);

            expect(parcelaMatch, 'Não encontrou número de parcelas').to.not.be.null;
            expect(valorMatch, 'Não encontrou valor total').to.not.be.null;

            const numParcelas = parseInt(parcelaMatch[1], 10);
            const valorTotal = parseFloat(valorMatch[1].replace(/\./g, '').replace(',', '.'));

            return { numParcelas, valorTotal };
        }

        // Valida antes do clique
        cy.get(this.elements.INDICADORES).invoke('text').then((text) => {
            const { numParcelas, valorTotal } = extrairIndicadores(text);
            expect(numParcelas, 'Número de parcelas antes do clique').to.equal(0);
            expect(valorTotal, 'Valor total antes do clique').to.equal(0);
        });

        cy.get(this.elements.CAMPO_CLIENTE).should('be.visible').should('contain.text', nomeCliente);
        cy.get(this.elements.EXPANDIR_PARCELA_CLIENTE).should('be.visible').click();
        cy.get(this.elements.SELECIONA_PARCELA_CLIENTE).should('be.visible').click();

        // Valida depois do clique
        cy.get(this.elements.INDICADORES).invoke('text').then((text) => {
            const { numParcelas, valorTotal } = extrairIndicadores(text);
            expect(numParcelas, 'Número de parcelas depois do clique').to.equal(quantidadeParcela);
            expect(valorTotal, 'Valor total depois do clique').to.equal(valorParcela);
        });
    }

    validarImpressaoContrato(nomeCliente: string): void {
        this.buscarCliente(nomeCliente)
        cy.get(this.elements.CHECK_TODAS_PARCELAS_ALUNO).check({ force: true })
        cy.window().then((win) => {
            cy.stub(win, 'open').callsFake((url) => {
                win.location.href = url;

            });
        })
        cy.get(this.elements.IMPRIMIR_CONTRATO).click()
        cy.wait(3000);
        cy.contains(nomeCliente)

    }

    validarBotoesEstado(estado: 'ativo' | 'inativo'): void {
        const botoes = [this.elements.BOTAO_RECEBER, this.elements.BOTAO_GERAR_BOLETO, this.elements.BOTAO_RENEGOCIAR, this.elements.CANCELAR_PARCELAS]

        const expectativa = estado === 'ativo' ? 'not.be.disabled' : 'be.disabled'

        botoes.forEach(seletor => {
            cy.get(seletor).should(expectativa)

        })

    }

    validarBotaoImprimir(estado: 'ativo' | 'inativo'): void {
        if (estado === 'ativo') {
            cy.get(this.elements.IMPRIMIR_CONTRATO).should('be.visible')
        } else if (estado === 'inativo') {
            cy.get(this.elements.IMPRIMIR_CONTRATO).should('not.exist')
        }
    }

    validarBloqueioEdicaoValorBoleto(nomeAluno): void {
        this.selecionarTodasParcelasCliente(nomeAluno)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        cy.get(this.elements.VALOR_BOLETO).should('be.disabled')
        cy.get('.pct-calendar').should('not.be.disabled')
    }

    gerarBoletoDataDiferente(nomeCliente: string): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        cy.get(this.elements.SELECIONA_CALENDARIO).should('not.be.disabled').click()
        cy.get(this.elements.DIA_VENCIMENTO_CALENDAR).click()

        const amanha = DateUtils.futureDateBR(5)
        cy.get(this.elements.COLUNA_DATA_VENCIMENTO_BOLETO).should('contain.text', amanha)

        // gerar boleto
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.contains('Boleto gerado com sucesso').should('be.visible')
        cy.visit(EnvUtils.admFrontUrl() + '/pt/adm')
        this.acessarTelaDoAlunoLupa(nomeCliente)
        cy.get(this.elements.ABA_PACTOPAY).click()
        cy.wait(3000)

        //Valida o valor total do boleto e data de vencimento alterada
        cy.contains('R$ 970,80').trigger('mouseover').should('be.visible')
        cy.contains(amanha).trigger('mouseover').should('be.visible')
    }

    gerarBoletoComMulta(nomeCliente: string): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        cy.contains('label', 'Considerar multa ou juros').click()
        cy.get(this.elements.CHECKM_MULTA_BOLETO).should('be.checked')
        cy.get(this.elements.TOTAL_PARCELAS)
            .find('span')
            .should('contain.text', 'R$ 0,01')

        //gerar boleto
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.contains('Boleto gerado com sucesso').should('be.visible')
        this.acessarTelaDoAlunoLupa(nomeCliente)
        cy.get(this.elements.ABA_PACTOPAY).click()
        cy.wait(3000)

        //Valida o valor total do boleto com juros
        cy.contains('R$ 970,81').trigger('mouseover').should('be.visible')


    }

    gerarBoleto(nomeCliente: string): void {
        this.selecionarTodasParcelasCliente(nomeCliente)
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.get(this.elements.SELECT_CONVENIO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('BOLETO BANCÁRIO PJBANK').click()
        //gerar boleto
        cy.contains('button', 'Gerar boleto').should('not.be.disabled').click()
        cy.contains('Boleto gerado com sucesso').should('be.visible')


    }

    compartilharBoleto(): void {
        cy.wait(300)
        cy.contains('Agora você pode compartilhar com o cliente para que o pagamento seja efetuado').should('be.visible')
        cy.get(this.elements.COMPARTILHAR_BOLETO).click()
        cy.get(this.elements.QR_CODE).should('be.visible')
        cy.get(this.elements.EMAIL).type('<EMAIL>')
        cy.get(this.elements.BOTAO_COMPARTILHAR_EMAIL).click()
        cy.contains('Email enviado com sucesso.', { timeout: 20000 }).should('be.visible')
        cy.get(this.elements.BOTAO_COPIAR_LINK).click()
        cy.contains('Link copiado para a sua área de transferência!', { timeout: 20000 }).should('be.visible')


    }

    validarSetaVoltar(): void {
        cy.wait(300)
        cy.get(this.elements.SETA_VOLTAR).should('be.visible').click()
        cy.contains('Caixa em aberto')
        cy.wait(3000)
        cy.get(this.elements.SETA_VOLTAR).should('not.exist')


    }

    imprimirContrato(pessoa) {
        cy.window().then((win) => {
            cy.stub(win, 'open').callsFake((url) => {
                win.location.href = url;

            });
        })
        cy.get(this.elements.IMPRIMIR_CONTRATO_RECEBER).click()
        cy.wait(2000)
        cy.get(this.elements.NOME_CONTRATO).contains(pessoa)
        cy.wait(5000)
    }

    enviarEmail(email) {
        cy.wait(2000)
        cy.get(this.elements.ENVIAR_CONTRATO_EMAIL).click()
        cy.get(this.elements.SELECIONAR_CONTRATO).click().wait(1000)
        cy.get('.modal-titulo').click().wait(1000)
        cy.get(this.elements.SELECIONAR_CONTRATO).click()
        cy.get(this.elements.PRIMEIRA_OPCAO_SELECIONAR_CONTRATO).click()
        cy.get(this.elements.ADICIONAR_EMAIL).type(email)
        cy.get(this.elements.BOTAO_ENVIAR_EMAIL).type(email)
    }

    receberNegociacao(pessoa) {
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).click({ force: true }).wait(2000)
        cy.get(this.elements.INPUT_RECEBER_NEGOCIACAO).type(pessoa).wait(3000)
        cy.get(this.elements.PRIMEIRA_BUSCA_NEGOCIACAO).click()
        cy.get(this.elements.BOTAO_RECEBER_NEGOCIACAO).click().wait(10000)
    }

    inserirSenha(): void {
        cy.contains('Autorizar acesso', { timeout: 3000 }).then(($el: JQuery<HTMLElement>) => {
            if ($el.length > 0) {
                cy.wait(300);
                cy.get(this.elements.INSERIR_SENHA).type('123');
                cy.get(this.elements.CONFIRMAR_SENHA).click();
            } else {
                cy.log('Senha não foi solicitada, pulando inserção.');
            }
        });
    }

    validarBloqueioPinpad(formaPagamento: string) {
        cy.wait(3000);
        cy.get(this.elements.SELECT_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains(`${formaPagamento} - ONLINE`).click()
        cy.contains('button', 'Receber').click()
        cy.get(this.elements.MENSAGEM_RETORNO, { timeout: 10000 }).should('be.visible').contains('Não Foi Possível Realizar esta Operação')
    }

    // Método alternativo para interagir com campo de data problemático
    interagirComCampoDataAlternativo(data: string): void {
        // Opção 1: Usar atributos específicos
        cy.get('input[ds3input][matinput][placeholder="dd/mm/aaaa"]')
            .should('exist')
            .scrollIntoView()
            .click({ force: true })
            .clear({ force: true })
            .type(data, { force: true, delay: 100 })
            .blur()

        // Opção 2: Se a primeira não funcionar, tenta com contains
        cy.get('body').then($body => {
            if ($body.find('input[placeholder="dd/mm/aaaa"]').length > 0) {
                cy.get('input[placeholder="dd/mm/aaaa"]')
                    .first()
                    .click({ force: true })
                    .clear({ force: true })
                    .type(data, { force: true })
                    .blur()
            }
        })
    }

    receberUsandoAutorizacao(aluno: string, usaSenha: boolean = true) {
        this.selecionarTodasParcelasCliente(aluno)
        cy.contains('button', 'Receber').click();
        cy.get(this.elements.SELECIONAR_FORMA_PAGAMENTO).click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains(' Cartão De Crédito - ONLINE ').click()
        cy.get('#pagamento-online-0-select-convenio > .ds3-select').click()
        cy.wait(3000);
        cy.contains('button', 'Usar').click();
        cy.get('#pagamento-online-0-select-convenio > .ds3-select').click()
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('TESTE VENDAS ONLINE').click()
        cy.get(this.elements.CVV_CARTAO).type('123');
        cy.get(this.elements.SELECIONAR_PARCELA).click();
        cy.get(this.elements.DS3_SELECT_OPTIONS).contains('12X').click()
        cy.get(this.elements.BOTAO_RECEBER_FINALIZAR).click()
        if (usaSenha) {
            cy.get(this.elements.INSERIR_SENHA).type('123')
            cy.get(this.elements.CONFIRMAR_SENHA).click()
        }
        cy.get(this.elements.MODAL_FINALIZAR, { timeout: 10000 }).contains('Pagamento realizado com sucesso');
    }

    validarAberturaPelaTelaAluno(aluno: string) {
        cy.get(this.elements.TITULO_PAGINA, { timeout: 10000 })
            .contains('Caixa em aberto')
            .should('be.visible')
        cy.get(this.elements.INPUT_BUSCA_RAPIDA).should('have.value', aluno)
    }
}

export default new NovoCaixaAbertoPage();
