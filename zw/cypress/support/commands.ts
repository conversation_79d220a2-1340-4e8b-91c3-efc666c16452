import EnvUtils from "@utils/EnvUtils";

Cypress.Commands.add('capturaPopUp', () => {
  cy.window().then((win) => {
    cy.stub(win, "open", (url) => {
      win.location.href = url
    }).as("popup")
  })
})

Cypress.Commands.add('visitarNegocicao', () => {
  cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/negociacao/contrato')
})

Cypress.Commands.add('visitarCaixaAberto', () => {
  cy.visit(EnvUtils.admFrontUrl() + '/pt/adm/caixa-em-aberto/lista')
})
