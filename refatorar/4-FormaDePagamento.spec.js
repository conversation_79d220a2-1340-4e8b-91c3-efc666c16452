/// <reference types="cypress"/>

describe('Login', function () {
    
        beforeEach(() => {
            cy.login('adm')
            cy.visit('faces/formaPagamentoCons.jsp')
            cy.wait(3000)
        })

        cy.on('uncaught:exception', err => {
            return false;
        });


        it('Testar forma de pagamento',()=>{
       
        cy.get('#form\\:btnNovo').click()
        cy.wait(1000)
        cy.get('#form\\:descricao').type('teste forma de pagamento')
        cy.get('#form\\:tipoFormaPagamento').select('BB')
        cy.waitVisible ('#form\\:convenio')
        cy.get('#form\\:convenio').select('26')  
        cy.get('#form\\:salvar').click({ force: true })
        cy.get('.mensagem').should('contain', 'Dados Gravados com Sucesso')
        
    })

    it('Testar forma de pagamento ja existente',()=>{

        cy.on('uncaught:exception', err => {
            return false;
        });

        cy.get('#form\\:btnNovo').click()
        cy.wait(1000)
        cy.get('#form\\:descricao').type('teste forma de pagamento')
        cy.get('#form\\:tipoFormaPagamento').select('BB')
        cy.waitVisible ('#form\\:convenio')
        cy.get('#form\\:convenio').select('26')  
        cy.get('#form\\:salvar').click({ force: true })
        cy.get('.mensagem').should('contain', 'Não Foi Possível Realizar esta Operação')
        
    })

    it('Selecionar  pontos Parceiro Fidelidade ',()=>{
       
        cy.get('#form\\:btnNovo').click()
        cy.wait(1000)
        cy.get('#form\\:descricao').type('teste forma de pagamento pontos fidelidade')
        cy.get('#form\\:tipoFormaPagamento').select('BB')
        cy.waitVisible ('#form\\:convenio')
        cy.get('#form\\:convenio').select('26')  
        cy.get('#form\\:gerarPontos').click()
        cy.get('#form\\:tipoParceiro').select('DOTZ')
        cy.get('#form\\:salvar').click({ force: true })
   
    })

    it('Inativar forma de pagamento existente ',()=>{
        cy.get('#tblFormaPagamento_lengthId').select('25')
        cy.wait(1000)
        cy.contains('TESTE FORMA DE PAGAMENTO').click()
        cy.get('#form\\:ativo').click()
        cy.waitVisible ('#form\\:salvar')
        cy.get('#form\\:salvar').click({ force: true })
        cy.get('.mensagem').should('contain', 'Dados Gravados com Sucesso')
    })

    it('Adicionar Empresa em conta já existente ',()=>{
        cy.get('#tblFormaPagamento_lengthId').select('25')
        cy.wait(1000)
        cy.contains('TESTE FORMA DE PAGAMENTO').click()
        cy.waitVisible ('#form\\:ativo')
        cy.get('#form\\:ativo').click()
        cy.waitVisible ('#form\\:empresaselecionadafpgto > tbody > .linhaImpar > .classDireita > .block > .form')
        cy.get('#form\\:empresaselecionadafpgto > tbody > .linhaImpar > .classDireita > .block > .form').select('NOME FANTASIA DA ACADEMIA')
        cy.waitVisible ('#form\\:adicionarCfgEmpresa')
        cy.get('#form\\:adicionarCfgEmpresa').click()
        cy.waitVisible ('#form\\:salvar')
        cy.get('#form\\:salvar').click({ force: true })
        cy.waitVisible ('.mensagem')
        cy.get('.mensagem').should('contain', 'Dados Gravados com Sucesso')
    })

    it('Adicionar forma de pagamento STONE CONNECT ',()=>{
        cy.get('#tblFormaPagamento_lengthId').select('25')
        cy.wait(1000)
        cy.contains('CARTÃO DE CRÉDITO').click()
        
        //cy.get('#form\\:salvar').click({ force: true })
       // cy.get('.mensagem').should('contain', 'Dados Gravados com Sucesso')
    })

   

 })