import DescontoporConvenio from '../../../support/pages/ADM/DescontoporConvenio';

describe('Testes de Desconto por Convênio', () => {
    before(() => {
        cy.logout();
    });

    beforeEach(() => {
        cy.login('adm', '28/05/2024');

    });

    it('Convênio sem isenção de produto obrigatório na matrícula', () => {
        DescontoporConvenio.convenioSemIsencaoMatricula();
    });

    it('Convênio sem isenção de produto obrigatório na rematrícula', () => {
        DescontoporConvenio.convenioSemIsencaoRematricula();
    });

    it('Convênio com isenção de produto obrigatório na matrícula', () => {
        DescontoporConvenio.convenioComIsencaoMatricula();
    });

    it('Convênio com isenção de produto obrigatório na rematrícula', () => {
        DescontoporConvenio.convenioComIsencaoRematriculaSemBv();
    });
});




