
import { FANTASIA } from "zw/cypress/support/Kula/models/EmpresasEnum"
import { NOVO_ADMINISTRATIVO } from "zw/cypress/support/Kula/models/ModulosEnum"
import LoginPage from "zw/cypress/support/Kula/pages/LOGIN/LoginPage"
import EnvUtils from "zw/cypress/support/Kula/utils/EnvUtils"
import MenuSuperiorPage from "zw/cypress/support/pages/angular/adm/MenuSuperiorPage"
import ResumoContaPage from "zw/cypress/support/pages/jsf/financeiro/ResumoContaPage"
import { ModulosEnum } from "zw/cypress/support/pages/angular/modulos-enum";


describe('Financeiro | Resumo de conta', () => {

    beforeEach(() => {
        LoginPage.logar(EnvUtils.usuarioPactoBr(), NOVO_ADMINISTRATIVO, FANTASIA)
    })

    it('SP-3861 - Resumo de conta, verificar se tela carregou com sucesso', () => {
        MenuSuperiorPage.buscarFuncionalidadesNaLupa('Recebíveis', { matchMode: "exact" })
        
       MenuSuperiorPage.menuExplorar('Resumo de Conta', ModulosEnum.MODULO_FINANCEIRO);
       ResumoContaPage.acessarConta('Banco (Custódia)')
       ResumoContaPage.voltar()

       ResumoContaPage.acessarConta('Banco da Empresa')
       ResumoContaPage.voltar()

       ResumoContaPage.acessarConta('Caixa Financeiro $ (Movimento)')
       ResumoContaPage.voltar()

       ResumoContaPage.acessarConta('Cartões Crédito (Cielo ou Redecard)')
       ResumoContaPage.voltar()

       ResumoContaPage.acessarConta('Cartões de Debito (Cielo ou Redecard)')
       ResumoContaPage.voltar()

       ResumoContaPage.acessarConta('Cofre (Cheques)')
       ResumoContaPage.voltar()

       ResumoContaPage.acessarConta('Pendência')
       ResumoContaPage.voltar()
      
       ResumoContaPage.acessarConta('Devoluções (Cheques devolvidos)')
       ResumoContaPage.voltar()
      
    })

})