import { FANTASIA } from "@models/EmpresasEnum"
import { NOVO_ADMINISTRATIVO } from "@models/ModulosEnum"
import LoginPage from "@pages/LOGIN/LoginPage"
import EnvUtils from "@utils/EnvUtils"
import MenuSuperiorPage from "support/pages/angular/adm/MenuSuperiorPage"
import PlanoPage from "support/pages/angular/adm/PlanoPage"

describe('Valida o cadastro de plano preenchendo os campos de observações em configurações avançadas', () => {
  beforeEach(() => {
    LoginPage.logar(EnvUtils.usuarioPactoBr(), NOVO_ADMINISTRATIVO, FANTASIA)
  })

  it('SP-4056 | Validar cadastro de plano preenchendo campos de observação', () => {
    MenuSuperiorPage.buscarFuncionalidadesNaLupa('Plano', {matchMode: 'exact'})
    const nomePlano = PlanoPage.criarPlanoComObservacoes()
    PlanoPage.validaTextoObservacao(nomePlano, 'Observações 1', 'Observações 2')
  })
})