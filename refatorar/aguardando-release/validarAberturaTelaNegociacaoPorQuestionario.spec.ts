import * as faker from 'faker-br';
import { FANTASIA } from "@models/EmpresasEnum"
import { NOVO_ADMINISTRATIVO } from "@models/ModulosEnum"
import LoginPage from "@pages/LOGIN/LoginPage"
import EnvUtils from "@utils/EnvUtils"
import HomeAdmPage from "support/pages/angular/adm/HomeAdmPage"
import PreCadastroPage from "support/pages/jsf/PreCadastroPage"
import NegociacaoPage from 'support/pages/angular/adm/NegociacaoPage';

describe('Valida se todos os fluxos da tela de questionário de BV encaminham para a nova tela de negociação', () => {
  beforeEach(() => {
    LoginPage.logar(EnvUtils.usuarioPactoBr(), NOVO_ADMINISTRATIVO, FANTASIA)
  })

  it('SP-4147 | Validar a nova tela de negociação ao clicar em Realizar Negociação na tela de questionário', () => {
    HomeAdmPage.botaoAdicionarPessoa()
    PreCadastroPage.iniciarCadastroCliente(faker.name.findName())
    NegociacaoPage.validaCarregamentoTela()
  })
})