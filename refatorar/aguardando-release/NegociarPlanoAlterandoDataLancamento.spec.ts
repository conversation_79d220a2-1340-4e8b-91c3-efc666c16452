import LoginPage from "@pages/LOGIN/LoginPage";
import EnvUtils from "@utils/EnvUtils";
import {NOVO_ADMINISTRATIVO} from "@models/ModulosEnum";
import NegociacaoPage from "support/pages/angular/adm/NegociacaoPage"
import PerfilAlunoValidacaoPage from "support/pages/angular/adm/PerfilAlunoValidacaoPage";
import AdmMsClientesApi from "@services/AdmMs/AdmMsClientesApi";
import {FANTASIA} from "@models/EmpresasEnum";
import MenuSuperiorPage from "support/pages/angular/adm/MenuSuperiorPage"
import DateUtils from "@utils/DateUtils";


describe('Negociacao de Planos', function () {

    beforeEach(() => {
        LoginPage.logarSemSessao(EnvUtils.usuarioPactoBr(), NOVO_ADMINISTRATIVO, 1);
    });

    it('Negociar um plano e alterar a data de lançamento', function () {
        const data = '01/06/2025'
        
        AdmMsClientesApi.incluirCliente(FANTASIA).then((aluno) => {
            const nome = aluno.pessoa.nome;
            MenuSuperiorPage.buscarFuncionalidadesNaLupa('Negociação', { matchMode: 'exact' });
            NegociacaoPage.lancarPlanoAlterandoData(nome, 'FAZ TUDO', data)
            PerfilAlunoValidacaoPage.validarContratoComDataAlterada(nome, data);
        });
    });

});




