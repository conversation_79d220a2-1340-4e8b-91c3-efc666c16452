import LoginPage from "@pages/LOGIN/LoginPage";
import EnvUtils from "@utils/EnvUtils";
import { NOVO_ADMINISTRATIVO } from "@models/ModulosEnum";
import CadastroClientePage from "support/pages/angular/adm/CadastroClientePage";


describe('Cadastrar Cliente', () => {
    beforeEach(() => {
        LoginPage.logarSemSessao(EnvUtils.usuarioEnzo(), NOVO_ADMINISTRATIVO, 1);
    });

    it('SP-4109| Cadastrar Cliente e Realizar Negociação', () => {
    CadastroClientePage.cadastrarClienteNegociacao();
  });
});