describe('Testes planos', function () {

    beforeEach(() => {
        cy.on('uncaught:exception', err => {
             return false;
         });
         cy.login('adm')
         cy.visit('faces/gestaoVendasOnline.jsp')
  
        })

it('inserir e editar plano', function(){
    cy.InserirPlano()

    //EDITANDO PLANO
        cy.wait(4000)
        cy.waitVisible('.testEditarPlano')
        cy.get('.testEditarPlano').eq(0).click()

        cy.wait(500)
        cy.waitVisible('.testDropPlano')
        cy.get('.testDropPlano').select('20')

        cy.wait(500)
        cy.waitVisible('.testDescricaoCabecalho')
        cy.get('.testDescricaoCabecalho').clear()

        cy.wait(500)
        cy.waitVisible('.testDescricaoCabecalho')
        cy.get('.testDescricaoCabecalho').type('cabeçalho editado')

        cy.wait(500)
        cy.waitVisible('.testDescricaoPlano')
        //cy.get('.testDescricaoPlano').clear()
        cy.get(':nth-child(3) > .w60 > div > .form').should('be.visible').clear()


        cy.wait(500)
        cy.waitVisible('.testDescricaoPlano')
        cy.get(':nth-child(3) > .w60 > div > .form').type('plano teste editado')

        cy.wait(500)
        cy.waitVisible('.testBeneficio01')
        cy.get(':nth-child(4) > .w60 > div > .form').clear()

        cy.wait(500)
        cy.waitVisible('.testBeneficio01')
        cy.get(':nth-child(4) > .w60 > div > .form').type('beneficio 1-ed')

        cy.wait(500)
        cy.waitVisible(':nth-child(4) > .w60 > div > .form')
        cy.get(':nth-child(5) > .w60 > div > .form').clear()

        cy.waitVisible(':nth-child(5) > .w60 > div > .form')
        cy.wait(500)
        cy.get(':nth-child(5) > .w60 > div > .form').type('beneficio 2-ed')

        cy.wait(500)
        cy.waitVisible(':nth-child(5) > .w60 > div > .form')
        cy.wait(5000)
        cy.get(':nth-child(6) > .w60 > div > .form').should('be.visible').clear()

        cy.wait(4000)
        cy.waitVisible('.testBeneficio03')
        cy.get(':nth-child(6) > .w60 > div > .form').type('beneficio 3-ed')

        cy.wait(4000)
        cy.waitVisible('.testPrecoOriginal')
        cy.get('.testPrecoOriginal').clear()

        cy.wait(4000)
        cy.waitVisible('.testPrecoOriginal')
        cy.get('.testPrecoOriginal').type('500,00')

        cy.wait(4000)
        cy.waitVisible('#form\\:gravarSiteConfig')
        cy.get('#form\\:gravarSiteConfig').click({ force: true })
        cy.wait(500)

    })

    it('excluir plano', function(){
        cy.visit('faces/gestaoVendasOnline.jsp')

        //Excluindo PLANOS
        cy.waitVisible('.testSite')
        cy.get('.testSite', {timeout:10000}).click()

        cy.wait(500)
        cy.waitVisible('#form\\:abrirPlanos')
        cy.get('#form\\:abrirPlanos', {timeout:10000}).click() 

        cy.wait(500)
        cy.get('#form\\:gravarSiteConfig').click({ force: true })
        cy.waitVisible('.testExclirPlano')
        cy.get('.testExclirPlano').eq(0).click({ force: true })
    })

})


