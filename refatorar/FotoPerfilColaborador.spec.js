import FotoPerfilColaborador from "../zw/cypress/support/pages/TREINO/FotoPerfilColaborador";

context('Valida foto dos modulos ADM e Treino', () => {
  it('Deve adicionar foto ao perfil do colaborador no ADM', () => {
    cy.loginComApi(1, 'teste', 'admin');
    FotoPerfilColaborador.adicionaFotoADM();
  });

  it('Deve validar que o hash da foto do ranking é igual a do ADM', () => {
    cy.loginComApi();
    FotoPerfilColaborador.validaFotoRanking();
  })

  it('Deve validar que o hash da foto da prescrição de treino é igual a do ADM', () => {
    cy.loginComApi();
    FotoPerfilColaborador.validaFotoPrescricao();
  })

  it('Deve validar que o hash da foto da tela de colaboradores treino é igual a do ADM', () => {
    cy.loginComApi();
    FotoPerfilColaborador.validaFotoCadastroColaborador();
  })
})




