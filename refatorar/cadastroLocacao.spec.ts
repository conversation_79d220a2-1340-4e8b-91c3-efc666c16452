import { FANTASIA } from "@models/EmpresasEnum"
import { TREINO } from "@models/ModulosEnum"
import LoginPage from "@pages/LOGIN/LoginPage"
import EnvUtils from "@utils/EnvUtils"
import MenuSuperiorPage from "support/pages/angular/adm/MenuSuperiorPage"
import LocacaoPage from "support/pages/angular/agenda/LocacaoPage"
import { ModulosEnum } from "support/pages/angular/modulos-enum"
import ProdutoPage from "support/pages/jsf/ProdutoPage"

describe('Valida o fluxo de cadastro de locação', () => {

  beforeEach(() => {
    cy.loginComApiNoSession()
  })

  it('SP-3870 | Validar cadastro de locação com tipo de horário livre', () => {
    cy.visit(EnvUtils.zwUrl() + '/faces/produtoCons.jsp?from=popup')
    ProdutoPage.cadastrarProdutoLocacao('10000').then(nomeProduto => {
      LoginPage.logar(EnvUtils.usuarioPactoBr(), TREINO, FANTASIA)
      MenuSuperiorPage.menuExplorar('Configurar Locações', ModulosEnum.MODULO_AGENDA)
      LocacaoPage.cadastrarLocacaoLivre(nomeProduto)
    })
  })

  it('SP-3871 | Validar cadastro de locação com tipo de horário Pré-definido', () => {
    cy.visit(EnvUtils.zwUrl() + '/faces/produtoCons.jsp?from=popup')
    ProdutoPage.cadastrarProdutoLocacao('10000').then(nomeProduto => {
      LoginPage.logar(EnvUtils.usuarioPactoBr(), TREINO, FANTASIA)
      MenuSuperiorPage.menuExplorar('Configurar Locações', ModulosEnum.MODULO_AGENDA)
      LocacaoPage.cadastrarLocacaoPreDefinida(nomeProduto)
    })
  })

})