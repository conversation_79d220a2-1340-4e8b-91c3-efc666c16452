import FixaAlunoNegociacao from "../../../support/pages/TREINO/FixaAlunoNegociacao";

beforeEach(() => {
  cy.loginComApi();
});

context('Valida a fixação de aluno na agenda na negociação de contrato', () => {
  it('Deve fixar um aluno com sucesso pela tela de negociação', () => {
    FixaAlunoNegociacao.atualizaAula();
    cy.CadastrarClienteComBV().then((aluno) => {
      const nome = aluno.replace('.', '');
      cy.menuExplorar('negociacao', 'ZW');
      FixaAlunoNegociacao.fixaAluno(nome);
      FixaAlunoNegociacao.confirmaAlunoEmTurma(nome);
    })
  })
});