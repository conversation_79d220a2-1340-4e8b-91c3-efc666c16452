// import { FANTASIA } from "zw/cypress/support/Kula/models/EmpresasEnum"
// import { NOVO_ADMINISTRATIVO } from "zw/cypress/support/Kula/models/ModulosEnum"
// import LoginPage from "zw/cypress/support/Kula/pages/LOGIN/LoginPage"
// import EnvUtils from "zw/cypress/support/Kula/utils/EnvUtils"
// import MenuSuperiorPage from "zw/cypress/support/pages/angular/adm/MenuSuperiorPage"
// import DREPage from "zw/cypress/support/pages/jsf/financeiro/DREPage"
// import { ModulosEnum } from "zw/cypress/support/pages/angular/modulos-enum";
// import NovaContaAPagarPage from "zw/cypress/support/pages/jsf/financeiro/NovaContaAPagarPage"
// import FinanceiroHomePage from "zw/cypress/support/pages/jsf/financeiro/FinanceiroHomePage"
//
// describe('Financeiro | DRE', () => {
//     it('SP-3860 - DRE, verificar se tela carregou com as informações corretas', () => {
//      LoginPage.logar(EnvUtils.usuarioPactoBr(), NOVO_ADMINISTRATIVO, FANTASIA)
//      MenuSuperiorPage.buscarFuncionalidadesNaLupa('Nova conta a pagar');
//      FinanceiroHomePage.abrirCaixa()
//      cy.wait(3000)
//      NovaContaAPagarPage.preencherLancamento(
//          'fornecedor teste',
//          'TESTE DRE',
//          '10000',
//          'DINHEIRO',
//          'Banco da Empresa'
//         )
//
//      NovaContaAPagarPage.agendarLancamento()
//      NovaContaAPagarPage.salvarLancamento()
//      NovaContaAPagarPage.senhaLancamento()
//      NovaContaAPagarPage.validarMensagemSucesso()
//
//      ///Inicio teste DRE////
//
//      LoginPage.logar(EnvUtils.usuarioPactoBr(), NOVO_ADMINISTRATIVO, FANTASIA)
//      MenuSuperiorPage.menuExplorar('DRE Financeiro', ModulosEnum.MODULO_FINANCEIRO);
//      DREPage.validarAberturaPagina()
//      DREPage.validarLancamentoFinanceiro('TESTE DRE')
//      DREPage.validarImpressao()
//     })
// })
