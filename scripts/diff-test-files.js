const { execSync } = require('child_process');

try {
    const gitCommand = 'git diff --name-only --diff-filter=d master';
    const output = execSync(gitCommand).toString();

    const files = output
        .split('\n')
        .filter(Boolean)
        .filter(file => file.includes('zw/cypress/e2e/'))
        .map(file => file.replace(/^zw\//, ''));

    const result = files.join(',');

    console.log(result);
} catch (error) {
    console.error('Erro ao executar o comando:', error.message);
    process.exit(1);
}