
const argparse = require('argparse');
const { resetInstancesWithErrors } = require('./cypress-sorry-api');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.add_argument('-c', '--ci-build-id', { 
    help: 'Especifica o código de execução do teste no cypress sorry' });

async function main() {
    const args = parser.parse_args();
    await resetInstancesWithErrors(args.ci_build_id);
}

main();