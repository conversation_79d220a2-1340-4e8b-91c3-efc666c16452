const axios = require('axios');
const { getDomainCypressSorryApiUrl } = require("../docker/swarm/lib/network");

async function getRunDetailsByCiBuildId(ciBuildId) {
    const runs = await getRunsFeed();
    const runByCiBuildId = runs.find(run => run.meta.ciBuildId === ciBuildId);

    if (!runByCiBuildId) {
        throw new Error(`No run found with ciBuildId: ${ciBuildId}`);
    }

    const runDetail = await getRunDetails(runByCiBuildId.runId);

    return runDetail;
}

async function getRunsFeed() {
    const headers = {
      "accept": "*/*",
      "accept-language": "pt,en-US;q=0.9,en;q=0.8",
      "content-type": "application/json"
    };
    
    const body = {
      operationName: "getRunsFeed",
      variables: {
        filters: [
          {
            key: "meta.projectId",
            value: "teste-auto",
            like: null
          }
        ],
        cursor: ""
      },
      query: `query getRunsFeed($cursor: String, $filters: [Filters!]!) {
        runFeed(cursor: $cursor, filters: $filters) {
          cursor
          hasMore
          runs {
            runId
            createdAt
            completion {
              ...RunSummaryCompletion
              __typename
            }
            meta {
              ...RunSummaryMeta
              __typename
            }
            progress {
              ...RunProgress
              __typename
            }
            __typename
          }
          __typename
        }
      }

      fragment RunSummaryCompletion on RunCompletion {
        completed
        inactivityTimeoutMs
        __typename
      }

      fragment RunSummaryMeta on RunMeta {
        ciBuildId
        projectId
        commit {
          sha
          branch
          remoteOrigin
          message
          authorEmail
          authorName
          __typename
        }
        __typename
      }

      fragment RunProgress on RunProgress {
        updatedAt
        groups {
          groupId
          instances {
            ...RunGroupProgressInstances
            __typename
          }
          tests {
            ...RunGroupProgressTests
            __typename
          }
          __typename
        }
        __typename
      }

      fragment RunGroupProgressInstances on RunGroupProgressInstances {
        overall
        claimed
        complete
        failures
        passes
        __typename
      }

      fragment RunGroupProgressTests on RunGroupProgressTests {
        overall
        passes
        failures
        pending
        skipped
        flaky
        __typename
      }
      `
    };

    try {
        const apiUrl = getDomainCypressSorryApiUrl() + '/';
        console.log('[CYPRESS API] Fazendo requisição para getRunsFeed:', apiUrl);
        const response = await axios.post(apiUrl, body, { headers: headers, timeout: 5000 });
        console.log('[CYPRESS API] Resposta getRunsFeed - Status:', response.status);
        return response.data.data.runFeed.runs;
    } catch (error) {
      console.error('[CYPRESS API ERROR] Erro na requisição getRunsFeed:');
      console.error('[CYPRESS API ERROR] URL tentada:', apiUrl);
      console.error('[CYPRESS API ERROR] Erro completo:', error.message);
      if (error.response) {
        console.error('[CYPRESS API ERROR] Status da resposta:', error.response.status);
        console.error('[CYPRESS API ERROR] Dados da resposta:', error.response.data);
      }
      if(error.message.includes('timeout')){
        console.error('Erro ao buscar execuções de teste. Verifique se você está conectado a VPN da Pacto Soluções e se sua internet está funcionando corretamente.');
      }else{
        console.error(error.message);
      }
      process.exit(1);
    }
}

async function getRunDetails(runId) {
    const apiUrl = getDomainCypressSorryApiUrl() + '/';
    console.log('[CYPRESS API] Fazendo requisição para getRunDetails:', apiUrl, 'runId:', runId);
    const response = await axios.post(apiUrl, {
        operationName: "getRun",
        variables: { runId },
        query: `query getRun($runId: ID!) {
            run(id: $runId) {
                runId
                createdAt
                completion {
                    ...RunSummaryCompletion
                    __typename
                }
                meta {
                    ...RunSummaryMeta
                    __typename
                }
                specs {
                    ...RunDetailSpec
                    __typename
                }
                progress {
                    ...RunProgress
                    __typename
                }
                __typename
            }
        }
        fragment RunSummaryCompletion on RunCompletion {
            completed
            inactivityTimeoutMs
            __typename
        }
        fragment RunSummaryMeta on RunMeta {
            ciBuildId
            projectId
            commit {
                sha
                branch
                remoteOrigin
                message
                authorEmail
                authorName
                __typename
            }
            __typename
        }
        fragment RunDetailSpec on RunSpec {
            instanceId
            spec
            claimedAt
            machineId
            groupId
            results {
                error
                flaky
                stats {
                    ...AllInstanceStats
                    __typename
                }
                __typename
            }
            __typename
        }
        fragment AllInstanceStats on InstanceStats {
            suites
            tests
            pending
            passes
            failures
            skipped
            suites
            wallClockDuration
            wallClockStartedAt
            wallClockEndedAt
            __typename
        }
        fragment RunProgress on RunProgress {
            updatedAt
            groups {
                groupId
                instances {
                    ...RunGroupProgressInstances
                    __typename
                }
                tests {
                    ...RunGroupProgressTests
                    __typename
                }
                __typename
            }
            __typename
        }
        fragment RunGroupProgressInstances on RunGroupProgressInstances {
            overall
            claimed
            complete
            failures
            passes
            __typename
        }
        fragment RunGroupProgressTests on RunGroupProgressTests {
            overall
            passes
            failures
            pending
            skipped
            flaky
            __typename
        }`
    }, {
        headers: {
            "accept": "*/*",
            "accept-language": "pt,en-US;q=0.9,en;q=0.8",
            "content-type": "application/json"
        },
        referrerPolicy: "strict-origin-when-cross-origin",
        withCredentials: false
    });

    console.log('[CYPRESS API] Resposta getRunDetails - Status:', response.status);

    if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
    }

    return response.data.data.run;
}

async function resetInstance(instanceId) {
    try {
        const apiUrl = getDomainCypressSorryApiUrl();
        console.log('[CYPRESS API] Fazendo requisição para resetInstance:', apiUrl, 'instanceId:', instanceId);
        const query = `
        mutation resetInstance($instanceId: ID!) {
            resetInstance(instanceId: $instanceId) {
                success
                message
                instanceId
                __typename
            }
        }
    `;

    const response = await axios.post(apiUrl, {
        operationName: "resetInstance",
        variables: { instanceId },
        query
    }, {
        headers: {
            "accept": "*/*",
            "accept-language": "pt,en-US;q=0.9,en;q=0.8",
            "content-type": "application/json"
        },
        referrer: "http://cypress.pactoteste.com",
        referrerPolicy: "strict-origin-when-cross-origin",
        withCredentials: false
    });

        console.log('[CYPRESS API] Resposta resetInstance - Status:', response.status);

        if (response.status !== 200) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.data.data.resetInstance;
    } catch (error) {
        console.error('[CYPRESS API ERROR] Erro na função resetInstance:');
        console.error('[CYPRESS API ERROR] instanceId:', instanceId);
        console.error('[CYPRESS API ERROR] Erro completo:', error.message);
        console.error('[CYPRESS API ERROR] Stack trace:', error.stack);
        if (error.response) {
            console.error('[CYPRESS API ERROR] Status da resposta:', error.response.status);
            console.error('[CYPRESS API ERROR] Dados da resposta:', error.response.data);
        }
        throw error;
    }
}

async function resetInstancesWithErrors(ciBuildId) {
    try {
        const runDetails = await getRunDetailsByCiBuildId(ciBuildId);

        const failedTests = runDetails.specs.filter(spec => spec.results && spec.results.stats && spec.results.stats.failures > 0);

        for (const spec of failedTests) {
            await resetInstance(spec.instanceId);
            console.log(`Instância resetada com sucesso: ${spec.instanceId}`);
            // Aguardar um pequeno intervalo para evitar sobrecarga na API
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.log("Reset de instâncias concluído com sucesso!");

    } catch (error) {
        console.error(`Erro ao resetar instâncias: ${error.message}`);
        // Tratar o erro conforme necessário
    }
}


module.exports = {
    getRunDetails,
    getRunDetailsByCiBuildId,
    getRunsFeed,
    resetInstancesWithErrors,
    resetInstance
};