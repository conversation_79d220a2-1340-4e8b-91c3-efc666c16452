#!/usr/bin/env node

const path = require('path');
const {deploy} = require(path.resolve(__dirname +'/../docker/swarm/lib/stack'));
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.add_argument('-s', '--stack-number', { help: 'Número da stack para deploy' });
parser.add_argument('-d', '--domain', { help: 'Domínio que serão utilizados na urls das aplicações', default: 'pactoteste.com'});
parser.add_argument('-i', '--ip', { help: 'Endereço IP do host aonde será deployado' });
parser.add_argument('-b', '--banch-name', { help: 'Nome da branch do repositorório do teste automatizado', default: 'master'});
parser.add_argument('-ab', '--app-branchs', { help: 'Informe os nomes das branches das aplicações, separados por vírgula.\n' +
'Por exemplo: zw=hotfix/change_version_commons_net,treino-front=feature/E2-840.\n' +
'Os nomes das aplicações (como "zw" e "treino-front") devem corresponder aos nomes definidos nos serviços dos templates do Docker Compose, como legacy.yml.template e front.yml.template, entre outros.' });
const args = parser.parse_args();

const run = async () => {
    try {
        await deploy(args.stack_number || 1, args.domain || 'pactoteste.com', args.ip, args.stack_number, args.banch_name, 'ci', args.app_branchs);
        process.exit(0);
    }catch(e){
        console.error(e);
        process.exit(1);
    }
}

run();
