#!/usr/bin/env node

const path = require('path');
const { deployTest } = require(path.resolve(__dirname + '/deploy-test'));
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.add_argument('-s', '--spec', { 
    nargs: '?',
    help: 'Especifica o arquivo de teste a ser executado. Por exemplo: **/0-Protocolos.spec.js' });
parser.add_argument('-r', '--replicas', { 
    default: 2,
    help: 'Especifica o número de testes em parallelo que serão executados' });
const args = parser.parse_args();

try {
    const ciBuildId = Math.floor(Math.random() * 900000) + 100000;
    deployTest(args.spec, ciBuildId, args.replicas);

}catch (error) {
    console.error(error);
    process.exit(1);
}  

process.exit(0);