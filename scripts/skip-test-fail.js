const fs = require('fs');
const path = require('path');

fs.readFile('test-result.json', 'utf8', (err, data) => {
    if (err) {
        console.error('Erro ao ler o arquivo:', err);
        return;
    }

    let json = JSON.parse(data);
    let results = json.data.testResults;

    results.forEach((item, index) => {
        if(!item.title) return;

        if(!results[index -1] || !results[index -1].spec || !results[index -1].spec.path) return;

        const specPath = results[index -1].spec.path;
        const zwFolder = path.resolve(__dirname, '..' , 'zw');
        const specFullPath = path.resolve(zwFolder, specPath);

        const fileContent = fs.readFileSync(specFullPath, 'utf8');
        let fileContentSkiped = fileContent;

        const titleTest = item.titleParts[item.titleParts.length -1];

        fileContent.split('\n').forEach((line, index2) => {
            if(line.includes(titleTest) && line.includes('it(') && !line.includes('it.skip(')) {
                fileContentSkiped = fileContentSkiped.replace(line, line.replace( 'it(', 'it.skip('));
            }
        });

        fs.writeFileSync(specFullPath, fileContentSkiped);

        console.log(`Test ${index + 1}: ${item.title} spec file: ${results[index -1].spec.path}`);
    });
});