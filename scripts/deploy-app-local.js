#!/usr/bin/env node

const path = require('path');
const { deploy } = require(path.resolve(__dirname +'/../docker/swarm/lib/stack'));
const { checkSwarmInit } = require(path.resolve(__dirname +'/../docker/swarm/lib/swarm'));
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.add_argument('-d', '--domain');
parser.add_argument('-i', '--ip',{required: false, default: null});
const args = parser.parse_args();

const run = async () => {
    try {
        checkSwarmInit();
        const CI_BUILD_ID = Math.floor(Math.random() * 900000) + 100000;
        await deploy(args.build_number || 1, 
            args.domain || 'test', 
            args.ip, 
            CI_BUILD_ID, 
            null, 
            'local');
        process.exit(0);
    }catch(e){
        console.error(e);
        process.exit(1);
    }
}

run();
