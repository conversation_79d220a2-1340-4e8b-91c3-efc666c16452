{"data": {"testResults": [{"__typename": "RunInstance", "id": "1a67009e-47b6-42e9-b239-0fad8f02da36", "totalPassed": 0, "totalFailed": 1, "totalPending": 0, "totalSkipped": 0, "status": "FAILED", "claimedAt": "2024-03-11T19:05:27.794Z", "completedAt": "2024-03-11T19:05:34.548Z", "hasStdout": true, "totalFlakyTests": 0, "totalMutedTests": 0, "prioritizedByFailedSpecs": true, "spec": {"id": "Spec:1a67009e-47b6-42e9-b239-0fad8f02da36", "path": "cypress/integration/7-Layout/8-LayoutAdmExplorarCadastros.spec.js", "shortPath": "7-Layout/8-LayoutAdmExplorarCadastros.spec.js", "basename": "8-LayoutAdmExplorarCadastros.spec.js", "__typename": "SpecFile"}, "duration": 344, "testingTypeEnum": "E2E", "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}}, {"__typename": "TestResult", "id": "c97c0d50-9a41-4266-923f-919d698614a4", "isMuted": false, "hasAssociatedJiraIssues": false, "title": "An uncaught error was detected outside of a test", "isFlaky": false, "state": "FAILED", "duration": 289, "titleHash": "c5964303-0e1e-7766-44fd-5d1d6aa79cfc", "titleParts": ["An uncaught error was detected outside of a test"], "bodyHash": "3407d3a6-2552-eab3-9545-c7363ac8727f", "videoTimestamp": null, "testId": "r2", "lastModification": null, "instance": {"id": "1a67009e-47b6-42e9-b239-0fad8f02da36", "duration": 344, "testingTypeEnum": "E2E", "spec": {"id": "Spec:1a67009e-47b6-42e9-b239-0fad8f02da36", "basename": "8-LayoutAdmExplorarCadastros.spec.js", "__typename": "SpecFile"}, "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "formattedName": "Chrome", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}, "__typename": "RunInstance"}, "capture": null, "run": {"id": "70a5f2f0-4769-4091-a666-ae732112d4cf", "isHiddenByUsageLimits": false, "isPastDataRetention": false, "configTestReplayEnabled": true, "cypressVersion": "9.7.0", "__typename": "Run"}}, {"__typename": "RunInstance", "id": "66405939-9c51-4503-8a79-83495d222b68", "totalPassed": 3, "totalFailed": 5, "totalPending": 1, "totalSkipped": 0, "status": "FAILED", "claimedAt": "2024-03-11T19:09:44.316Z", "completedAt": "2024-03-11T19:10:55.807Z", "hasStdout": true, "totalFlakyTests": 0, "totalMutedTests": 0, "prioritizedByFailedSpecs": false, "spec": {"id": "Spec:66405939-9c51-4503-8a79-83495d222b68", "path": "cypress/integration/2-Cadastros/2-ConfigFinanceira/0-ConvenioDeCobranca/4-ConvenioCobrancaPagoLivre.spec.js", "shortPath": "2-<PERSON><PERSON><PERSON>s/2-ConfigFinanceira/0-ConvenioDeCobranca/4-ConvenioCobrancaPagoLivre.spec.js", "basename": "4-ConvenioCobrancaPagoLivre.spec.js", "__typename": "SpecFile"}, "duration": 68332, "testingTypeEnum": "E2E", "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}}, {"__typename": "TestResult", "id": "d4722bf1-9299-4e97-bd73-5765c9840996", "isMuted": false, "hasAssociatedJiraIssues": false, "title": "Cadastro Convenio de Cobrança Pago Livre > Cadastrar convênio corretamente", "isFlaky": false, "state": "FAILED", "duration": 8277, "titleHash": "ca4cf1e0-e950-9337-861d-a31315ed018f", "titleParts": ["Cadastro Convenio de Cobrança Pago Livre", "Cadastrar convênio corretamente"], "bodyHash": "4cd70f71-dcdb-1822-eae6-0ef1c397546c", "videoTimestamp": null, "testId": "r5", "lastModification": null, "instance": {"id": "66405939-9c51-4503-8a79-83495d222b68", "duration": 68332, "testingTypeEnum": "E2E", "spec": {"id": "Spec:66405939-9c51-4503-8a79-83495d222b68", "basename": "4-ConvenioCobrancaPagoLivre.spec.js", "__typename": "SpecFile"}, "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "formattedName": "Chrome", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}, "__typename": "RunInstance"}, "capture": null, "run": {"id": "70a5f2f0-4769-4091-a666-ae732112d4cf", "isHiddenByUsageLimits": false, "isPastDataRetention": false, "configTestReplayEnabled": true, "cypressVersion": "9.7.0", "__typename": "Run"}}, {"__typename": "TestResult", "id": "ab912eb0-2251-4d49-b086-93418ed8a81a", "isMuted": false, "hasAssociatedJiraIssues": false, "title": "Cadastro Convenio de Cobrança Pago Livre > Editar <PERSON>", "isFlaky": false, "state": "FAILED", "duration": 7177, "titleHash": "c24b1156-0eab-a3c7-1cd0-671f8a24cb79", "titleParts": ["Cadastro Convenio de Cobrança Pago Livre", "<PERSON><PERSON>"], "bodyHash": "adb79bcb-1f4b-8dde-70b5-74bfcac6a112", "videoTimestamp": null, "testId": "r6", "lastModification": null, "instance": {"id": "66405939-9c51-4503-8a79-83495d222b68", "duration": 68332, "testingTypeEnum": "E2E", "spec": {"id": "Spec:66405939-9c51-4503-8a79-83495d222b68", "basename": "4-ConvenioCobrancaPagoLivre.spec.js", "__typename": "SpecFile"}, "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "formattedName": "Chrome", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}, "__typename": "RunInstance"}, "capture": null, "run": {"id": "70a5f2f0-4769-4091-a666-ae732112d4cf", "isHiddenByUsageLimits": false, "isPastDataRetention": false, "configTestReplayEnabled": true, "cypressVersion": "9.7.0", "__typename": "Run"}}, {"__typename": "TestResult", "id": "6525c998-5111-4b7b-a233-d773a0a79503", "isMuted": false, "hasAssociatedJiraIssues": false, "title": "Cadastro Convenio de Cobrança Pago Livre > Configurar Operadora de cartão", "isFlaky": false, "state": "FAILED", "duration": 6738, "titleHash": "4f710753-c58f-409c-f738-ab4b45ff7a4a", "titleParts": ["Cadastro Convenio de Cobrança Pago Livre", "Configurar Operadora de cartão"], "bodyHash": "aa90ab6a-0b8f-b168-2cc2-4c700119a519", "videoTimestamp": null, "testId": "r7", "lastModification": null, "instance": {"id": "66405939-9c51-4503-8a79-83495d222b68", "duration": 68332, "testingTypeEnum": "E2E", "spec": {"id": "Spec:66405939-9c51-4503-8a79-83495d222b68", "basename": "4-ConvenioCobrancaPagoLivre.spec.js", "__typename": "SpecFile"}, "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "formattedName": "Chrome", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}, "__typename": "RunInstance"}, "capture": null, "run": {"id": "70a5f2f0-4769-4091-a666-ae732112d4cf", "isHiddenByUsageLimits": false, "isPastDataRetention": false, "configTestReplayEnabled": true, "cypressVersion": "9.7.0", "__typename": "Run"}}, {"__typename": "TestResult", "id": "9dd0ba75-92ae-4d85-b982-1a921a1b474b", "isMuted": false, "hasAssociatedJiraIssues": false, "title": "Cadastro Convenio de Cobrança Pago Livre > Alterar convênio para homologação e inativar", "isFlaky": false, "state": "FAILED", "duration": 7137, "titleHash": "3bfc725b-f6aa-4ddc-dd12-09ef24878212", "titleParts": ["Cadastro Convenio de Cobrança Pago Livre", "Alterar convênio para homologação e inativar"], "bodyHash": "0e9321b9-8327-a6e7-708e-da296088831c", "videoTimestamp": null, "testId": "r8", "lastModification": null, "instance": {"id": "66405939-9c51-4503-8a79-83495d222b68", "duration": 68332, "testingTypeEnum": "E2E", "spec": {"id": "Spec:66405939-9c51-4503-8a79-83495d222b68", "basename": "4-ConvenioCobrancaPagoLivre.spec.js", "__typename": "SpecFile"}, "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "formattedName": "Chrome", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}, "__typename": "RunInstance"}, "capture": null, "run": {"id": "70a5f2f0-4769-4091-a666-ae732112d4cf", "isHiddenByUsageLimits": false, "isPastDataRetention": false, "configTestReplayEnabled": true, "cypressVersion": "9.7.0", "__typename": "Run"}}, {"__typename": "TestResult", "id": "71465373-d9dd-4df6-9a97-5e4e619b50ae", "isMuted": false, "hasAssociatedJiraIssues": false, "title": "Cadastro Convenio de Cobrança Pago Livre > Excluir", "isFlaky": false, "state": "FAILED", "duration": 6557, "titleHash": "bb5edfaa-b3bf-44f3-205c-ea8f26b89ea0", "titleParts": ["Cadastro Convenio de Cobrança Pago Livre", "Excluir"], "bodyHash": "8dc2f39e-ac56-52c9-c726-919bc5eb523b", "videoTimestamp": null, "testId": "r9", "lastModification": null, "instance": {"id": "66405939-9c51-4503-8a79-83495d222b68", "duration": 68332, "testingTypeEnum": "E2E", "spec": {"id": "Spec:66405939-9c51-4503-8a79-83495d222b68", "basename": "4-ConvenioCobrancaPagoLivre.spec.js", "__typename": "SpecFile"}, "os": {"name": "Linux", "version": "Debian - 11.3", "__typename": "OperatingSystem"}, "browser": {"name": "chrome", "version": "100", "formattedNameWithVersion": "Chrome 100", "formattedName": "Chrome", "__typename": "BrowserInfo"}, "group": {"id": "linux-Chrome-100-9aef98d3eb", "name": "", "browser": {"formattedNameWithVersion": "Chrome 100", "__typename": "BrowserInfo"}, "os": {"nameWithVersion": "Linux Debian - 11.3", "__typename": "OperatingSystem"}, "__typename": "RunGroup"}, "__typename": "RunInstance"}, "capture": null, "run": {"id": "70a5f2f0-4769-4091-a666-ae732112d4cf", "isHiddenByUsageLimits": false, "isPastDataRetention": false, "configTestReplayEnabled": true, "cypressVersion": "9.7.0", "__typename": "Run"}}]}}