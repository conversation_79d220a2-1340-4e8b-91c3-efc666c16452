#!/usr/bin/env node

const path = require('path');
const { deployTest } = require(path.resolve(__dirname + '/deploy-test'));
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();

parser.add_argument('-sp', '--spec', { 
    nargs: '?',
    help: 'Especifica o arquivo de teste a ser executado. Por exemplo: **/0-Protocolos.spec.js' });
parser.add_argument('-s', '--stack-number', { 
    help: 'Número do build do CI', default: process.env.CI_PIPELINE_ID });
parser.add_argument('-r', '--replicas', { 
    default: 2,
    help: 'Especifica o número de testes em parallelo que serão executados' });
parser.add_argument('-cs', '--cypress-sorry', { 
        default: true,
        help: 'Define se o paralelismo do teste será coordenado pelo cypress sorry: http://cypress.pactoteste.com/teste-auto' });
parser.add_argument('-t', '--tags', { 
    nargs: '?',
    help: 'Tags que serão associadas a exeucção do cypress, separadas por virgula'});
parser.add_argument('-c', '--commit-message', {
    nargs: '?',
    help: 'Mensagem de commit que será exibida no cypress'
})
parser.add_argument('-bn', '--burn', {
    nargs: '?',
    help: 'Quantidade de vezes que cada teste será repetido'
})
const args = parser.parse_args();

console.log('🎬 [DEPLOY TEST CI] Iniciando deploy-test-ci.js...');
console.log('🌍 [DEPLOY TEST CI] Variáveis de ambiente importantes:');
console.log('   - CI_PIPELINE_ID:', process.env.CI_PIPELINE_ID);
console.log('   - TEST_ENVIRONMENT:', process.env.TEST_ENVIRONMENT);
console.log('   - DOMAIN:', process.env.DOMAIN);
console.log('   - URL_CYPRESS_SORRY_API:', process.env.URL_CYPRESS_SORRY_API);

console.log('📋 [DEPLOY TEST CI] Argumentos parseados:');
console.log('   - spec:', args.spec);
console.log('   - stack_number:', args.stack_number);
console.log('   - replicas:', args.replicas);
console.log('   - cypress_sorry:', args.cypress_sorry);
console.log('   - tags:', args.tags);
console.log('   - commit_message:', args.commit_message);
console.log('   - burn:', args.burn);

try {
    console.log('🚀 [DEPLOY TEST CI] Chamando deployTest...');
    deployTest(args.spec, args.stack_number, args.replicas, args.cypress_sorry, 'PIPE', args.tags, args.commit_message, args.burn);
    console.log('✅ [DEPLOY TEST CI] deployTest concluído com sucesso!');
}catch (error) {
    console.error('❌ [DEPLOY TEST CI] Erro no deployTest:', error);
    process.exit(1);
}

console.log('🏁 [DEPLOY TEST CI] deploy-test-ci.js finalizado!');
process.exit(0);