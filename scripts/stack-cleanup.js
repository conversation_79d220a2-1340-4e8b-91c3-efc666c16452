#!/usr/bin/env node

const {execSync} = require('child_process');
const argparse = require('argparse');
const ArgumentParser = argparse.ArgumentParser;
const parser = new ArgumentParser();
parser.add_argument('-p', '--remove-proxy', {default: 'false'});
const args = parser.parse_args();

const cleanUp = async () => {
    try {

        if (args.remove_proxy === 'true') {
            const resultRemoveProxy = execSync(`docker stack rm proxy`).toString();
            console.info(resultRemoveProxy);
        }

        const stacks = execSync("docker stack ls --format '{{.Name}}'").toString().split('\n');
        const filteredStacks = stacks.filter(name => name.startsWith('test-') || name.startsWith('pipe-'));
        filteredStacks.forEach(stack => {
            try {
                const resultRmStack = execSync(`docker stack rm ${stack}`).toString();
                console.info(resultRmStack);
            } catch (e) {
                console.error(`Failed to remove stack: ${stack}`);
                console.error(e);
            }
        });


        const traefikService = execSync('docker service ls --format "{{.Name}}" | grep traefik')
            .toString().trim();
        console.log(`Serviço do Traefik encontrado: ${traefikService}`);

        const networks = execSync("docker network ls --format '{{.Name}}'").toString().split('\n');
        const filteredNetworks = networks.filter(name => name.startsWith('network-') || name.startsWith('test-'));
        filteredNetworks.forEach(networkName => {
            try {
                // Remove a rede do serviço Traefik
                execSync(`docker service update --network-rm ${networkName} ${traefikService}`);
                console.log(`Network ${networkName} removida do serviço ${traefikService}`);

                // Tenta remover a rede
                execSync(`docker network rm ${networkName}`);
                console.log(`Network ${networkName} removida.`);

            } catch (e) {
                console.error(`Failed to remove network: ${networkName}`);
                console.error(e);
            }
        });


    } catch (e) {
        console.error(e);
        process.exit(1);
    }
}

cleanUp();
