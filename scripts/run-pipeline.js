#!/usr/bin/env node

const axios = require('axios');
const inquirer = require('inquirer');
const fs = require('fs');
const path = require('path');
const argparse = require('argparse');
const { getBranchs, getOriginalBranchName } = require('./git');

const parser = new argparse.ArgumentParser({
    description: 'Run pipeline script'
});

parser.add_argument('-e', '--environment', { help: 'Environment to run the pipeline' });
parser.add_argument('-b', '--buildCypress', { help: 'Build Cypress container', type: 'bool', default: false });
parser.add_argument('-c', '--cleanup', { help: 'Cleanup environment', type: 'bool', default: false });
parser.add_argument('-br', '--branch', { help: 'Branch to run the pipeline' });
parser.add_argument('--commitMessage', { help: 'Commit message for Cypress' });
parser.add_argument('--spec', { help: 'Cypress spec path' });
parser.add_argument('--replicas', { help: 'Number of Cypress replicas', default: '35' });
parser.add_argument('--sorry', { help: 'Use Cypress Sorry', type: 'bool', default: true });
parser.add_argument('--cleanupProxy', { help: 'Cleanup proxy', type: 'bool', default: false });
parser.add_argument('--appBranchs', { help: 'Application branches', default: 'zw=master,treino-front=master' });
parser.add_argument('--testRetry', { help: 'Retry failed tests', type: 'bool', default: true });

const args = parser.parse_args();

let gitlabToken;
const projectId = '33594913';
const url = `https://gitlab.com/api/v4/projects/${projectId}/pipeline`;
const gitlabTokenFilePath = path.resolve(__dirname, 'gitlab-token.txt');

fs.readFile(gitlabTokenFilePath, 'utf8', (err, data) => {
    if (err) {
        inquirer
            .prompt([
                {
                    type: 'password',
                    message: 'Enter your GitLab token',
                    name: 'gitlabToken',
                },
            ])
            .then((answers) => {
                gitlabToken = answers.gitlabToken;
                fs.writeFile(gitlabTokenFilePath, gitlabToken, (err) => {
                    if (err) throw err;
                });
                runPipeline();
            });
    } else {
        gitlabToken = data;
        runPipeline();
    }
});

function runPipeline() {
    const options = {
        headers: {
            'Content-Type': 'application/json',
            'PRIVATE-TOKEN': gitlabToken,
        },
    };

    const branchs = getBranchs();
    const branch = getOriginalBranchName();

    const questions = [
        {
            type: 'list',
            name: 'environment',
            message: 'Em qual ambiente você deseja executar a pipeline?',
            choices: ['PROD', 'STAGING', 'ZW74', 'ZWALL'],
            when: () => !args.environment
        },
        {
            type: 'confirm',
            name: 'buildCypress',
            message: 'Você precisa buildar o container do Cypress?',
            default: false,
            when: () => args.buildCypress === undefined
        },
        {
            type: 'confirm',
            name: 'cleanup',
            message: 'Você deseja executar a limpeza no ambiente?',
            default: false,
            when: () => args.cleanup === undefined
        },
        {
            type: 'list',
            name: 'branch',
            message: 'Selecione qual branch do teste você quer executar a pipeline?',
            choices: branchs,
            default: branch,
            when: () => !args.branch
        }
    ];

    inquirer.prompt(questions).then((answers) => {
        const environment = args.environment || answers.environment;
        const buildCypress = args.buildCypress !== undefined ? args.buildCypress : answers.buildCypress;
        const cleanup = args.cleanup !== undefined ? args.cleanup : answers.cleanup;
        const selectedBranch = args.branch || answers.branch;

        const variables = getVariablesByEnvironment(environment);

        variables.push({ key: 'BUILD_CYPRESS', value: buildCypress ? 'true' : 'false' });
        variables.push({ key: 'CLEANUP', value: cleanup ? 'true' : 'false' });
        variables.push({ key: 'CYPRESS_COMMIT_MESSAGE', value: args.commitMessage || '' });
        variables.push({ key: 'CYPRESS_SPEC', value: args.spec || '' });
        variables.push({ key: 'CYPRESS_REPLICAS', value: args.replicas });
        variables.push({ key: 'CYPRESS_SORRY', value: args.sorry ? 'true' : 'false' });
        variables.push({ key: 'CLEANUP_PROXY', value: args.cleanupProxy ? 'true' : 'false' });
        variables.push({ key: 'APP_BRANCHS', value: args.appBranchs });
        variables.push({ key: 'TEST_RETRY', value: args.testRetry ? 'true' : 'false' });

        axios
            .post(url,
                {
                    ref: selectedBranch,
                    variables: variables
                },
                options)
            .then((res) => {
                console.log('Pipeline iniciada: ', res.data.web_url);
            })
            .catch((error) => console.error(error.message));
    });
}

function getVariablesByEnvironment(environment) {
    const environments = {
        PROD: [
            { key: 'TEST_ENVIRONMENT', value: 'PROD' },
            { key: 'CYPRESS_REPLICAS', value: '35'}
        ],
        ZW74: [
            { key: 'TEST_ENVIRONMENT', value: 'ZW74' },
            { key: 'CYPRESS_REPLICAS', value: '1'},
            {
                key: 'CYPRESS_SPEC',
                value: 'cypress/integration/1-TelaPrincipal/0-AcessoModulos/AcessoModulos&Fucionalidades.spec.js'
            },
        ],
        ZWALL: [
            { key: 'TEST_ENVIRONMENT', value: 'ZWALL' },
            { key: 'CYPRESS_REPLICAS', value: '1'},
            {
                key: 'CYPRESS_SPEC',
                value: 'cypress/integration/1-TelaPrincipal/0-AcessoModulos/AcessoModulos&Fucionalidades.spec.js'
            },
        ],
        STAGING: [
            { key: 'TEST_ENVIRONMENT', value: 'STAGING' },
            { key: 'CYPRESS_REPLICAS', value: '4'}
        ],
    };

    return environments[environment];
}
