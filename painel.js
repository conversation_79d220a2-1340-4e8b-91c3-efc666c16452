const express = require('express');
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process'); // Para executar comandos do sistema
const app = express();
let open;
const PORT = 3000;

const projectId = "33594913"; // Substitua pelo ID do projeto

// Função para ler o token de um arquivo
function getToken(filePath) {
  try {
    return fs.readFileSync(filePath, 'utf8').trim();
  } catch (error) {
    console.error(`Erro ao ler o token do arquivo ${filePath}:`, error.message);
    return null;
  }
}

// Caminhos dos arquivos de token
const gitlabTokenFilePath = path.join(__dirname, 'scripts', 'gitlab-token.txt');
const swarmTokenFilePath = path.join(__dirname, 'scripts', 'swarmpit-token.txt');

// Carrega os tokens diretamente dos arquivos
const GITLAB_TOKEN = getToken(gitlabTokenFilePath);
const SWARMPIT_TOKEN = getToken(swarmTokenFilePath);

if (!GITLAB_TOKEN || !SWARMPIT_TOKEN) {
  console.error("Erro: Não foi possível carregar os tokens. Verifique os arquivos de token.");
  process.exit(1);
}

// Servir arquivos estáticos da pasta public
app.use(express.static(path.join(__dirname, 'public')));

// Endpoint para obter as pipelines com status
app.get('/api/pipelines', async (req, res) => {
  try {
    const pipelinesResponse = await axios.get(`https://gitlab.com/api/v4/projects/${projectId}/pipelines`, {
      headers: {
        "PRIVATE-TOKEN": GITLAB_TOKEN,
      },
    });

    const pipelines = pipelinesResponse.data.slice(0, 10); // Pega as 10 últimas pipelines
    const deployedStackIds = await getDeployedStacksOnSwarm();

    // Mapear pipelines com status ativo/inativo com base no Swarm
    const pipelinesWithStatus = pipelines.map((pipeline) => {
      const stack = deployedStackIds.find(stack => stack.id === pipeline.id.toString());
      const status = stack ? 'Ativa' : 'Inativa';
      const commitMessage = stack && stack.commitMessage ? stack.commitMessage : 'Não disponível';

      return {
        id: pipeline.id,
        status: pipeline.status,
        executedAt: new Date(pipeline.created_at).toLocaleString("pt-BR"),
        deploymentStatus: status,
        commitMessage: commitMessage
      };
    });

    res.json(pipelinesWithStatus);
  } catch (error) {
    console.error('Erro ao buscar pipelines:', error.message);
    res.status(500).json({ error: 'Erro ao buscar pipelines' });
  }
});

// Endpoint para executar o script connect-pipe.js com o pipeline selecionado e, em seguida, executar `npm run open`
app.post('/api/connect-pipeline', express.json(), (req, res) => {
  const { pipelineId } = req.body;

  if (!pipelineId) {
    return res.status(400).json({ error: "ID da pipeline é obrigatório" });
  }

  const scriptPath = path.join(__dirname, 'scripts', 'connect-pipe.js');
  
  // Executa o script `connect-pipe.js` passando o ID da pipeline como argumento
  exec(`node ${scriptPath} -p ${pipelineId}`, (error, stdout, stderr) => {
    if (error) {
      console.error(`Erro ao executar o script: ${error.message}`);
      return res.status(500).json({ error: 'Erro ao executar o script' });
    }
    if (stderr) {
      console.error(`Stderr: ${stderr}`);
    }
    console.log(`Script output: ${stdout}`);

    // Após o script ser executado com sucesso, executa `npm run open`
    exec('npm run open', (err, stdoutOpen, stderrOpen) => {
      if (err) {
        console.error(`Erro ao executar npm run open: ${err.message}`);
        return res.status(500).json({ error: 'Erro ao executar npm run open' });
      }
      if (stderrOpen) {
        console.error(`Stderr (npm run open): ${stderrOpen}`);
      }
      console.log(`npm run open output: ${stdoutOpen}`);
      
      res.json({ message: `Pipeline ${pipelineId} conectada com sucesso e testes iniciados!` });
    });
  });
});

// Função para obter as stacks `deployed` do Swarm e extrair o valor de `COMMIT_INFO_MESSAGE`
async function getDeployedStacksOnSwarm() {
  const apiUrl = 'http://**********:888/api/stacks';
  try {
    const response = await axios.get(apiUrl, {
      headers: {
        'Accept': 'application/json',
        'Authorization': SWARMPIT_TOKEN,
      },
    });

    // Processa as stacks e extrai o valor de `COMMIT_INFO_MESSAGE`
    const deployedStacks = response.data
      .filter(stack => stack.state === 'deployed')
      .map(stack => {
        const commitInfo = stack.env && Array.isArray(stack.env) 
          ? stack.env.find(envVar => envVar.name === 'COMMIT_INFO_MESSAGE')
          : null;
        return {
          id: stack.stackName.replace('test-', ''), // Extrai o número do ID
          commitMessage: commitInfo ? commitInfo.value.replace(/^"|"$/g, '') : 'Sem informação'
        };
      });

    return deployedStacks;
  } catch (error) {
    console.error('Erro ao verificar stacks no Swarm:', error.message);
    return [];
  }
}

// Inicia o servidor
app.listen(PORT, () => {
  (async () => {
    open = (await import('open')).default;
    open('http://localhost:3000');
  })();
  console.log(`Servidor rodando em http://localhost:${PORT}`);
});
